/**
 * OCR标注工具 - 数据管理器
 * 负责JSON数据的处理、验证、导入导出
 */

class DataManager {
    constructor() {
        this.currentData = null;
        this.autoSaveInterval = 30000; // 30秒自动保存
        this.autoSaveTimer = null;
        this.storageKey = 'ocr_annotation_data';
        
        this.callbacks = {
            onDataLoad: [],
            onDataSave: [],
            onDataExport: [],
            onDataImport: []
        };

        this.startAutoSave();
    }

    /**
     * 添加回调函数
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 触发回调函数
     * @param {string} event 事件名称
     * @param {any} data 传递的数据
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    /**
     * 开始自动保存
     */
    startAutoSave() {
        this.stopAutoSave();
        this.autoSaveTimer = setInterval(() => {
            this.autoSave();
        }, this.autoSaveInterval);
    }

    /**
     * 停止自动保存
     */
    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
    }

    /**
     * 自动保存
     */
    autoSave() {
        if (this.currentData) {
            this.saveToLocalStorage();
        }
    }

    /**
     * 从标注数据生成JSON格式（支持多道题）
     * @param {Object} imageInfo 图片信息
     * @param {Array} annotations 标注数组
     * @param {Object} questionInfo 题目信息
     * @returns {Object} JSON数据
     */
    generateJSONFromAnnotations(imageInfo, annotations, questionInfo) {
        const mainQuestions = annotations.filter(ann => ann.type === 'main-question')
            .sort((a, b) => a.number - b.number);

        if (mainQuestions.length === 0) {
            throw new Error('缺少大题标注');
        }

        const subQuestions = annotations.filter(ann => ann.type === 'sub-question')
            .sort((a, b) => a.number - b.number);

        const answerAreas = annotations.filter(ann => ann.type === 'answer-area')
            .sort((a, b) => a.number - b.number);

        const imageAreas = annotations.filter(ann => ann.type === 'image-area')
            .sort((a, b) => a.number - b.number);

        // 构建JSON结构，支持多道题
        const jsonData = {};

        // 为每道大题构建数据结构
        mainQuestions.forEach(mainQuestion => {
            const mainQuestionKey = `大题${mainQuestion.number}`;

            // 获取该大题下的题干文字框
            const questionTexts = annotations.filter(ann =>
                ann.type === 'question-text' && ann.parentId === mainQuestion.id
            ).sort((a, b) => a.number - b.number);

            // 优先使用题干文字框的内容，如果没有则使用大题的content
            const questionTextContent = questionTexts.length > 0 ?
                (questionTexts[0].attributes.content || "") :
                (mainQuestion.attributes.content || "");

            console.log('🔍 [JSON生成调试] 大题', mainQuestion.number, '题干文字来源:',
                questionTexts.length > 0 ? '题干文字框' : '大题自身',
                '内容:', questionTextContent);

            jsonData[mainQuestionKey] = {
                "坐标": mainQuestion.coordinates,
                "题型": mainQuestion.attributes.questionType || "填空题",
                "题干文字": questionTextContent,
                "题目是否带配图": mainQuestion.attributes.hasImage ? "是" : "否"
            };

            // 找到属于这道大题的配图区域
            const relatedImageAreas = imageAreas.filter(imageArea =>
                imageArea.parentId === mainQuestion.id ||
                this.isAnnotationInside(imageArea, mainQuestion)
            );

            // 添加配图区域坐标
            if (relatedImageAreas.length > 0) {
                jsonData[mainQuestionKey]["配图区域坐标"] = {};
                relatedImageAreas.forEach(imageArea => {
                    jsonData[mainQuestionKey]["配图区域坐标"][`配图${imageArea.number}`] = imageArea.coordinates;
                });
            }

            // 找到属于这道大题的小题
            const relatedSubQuestions = subQuestions.filter(subQuestion =>
                subQuestion.parentId === mainQuestion.id ||
                this.isAnnotationInside(subQuestion, mainQuestion)
            );

            // 添加小题
            if (relatedSubQuestions.length > 0) {
                jsonData[mainQuestionKey]["小题"] = {};

                relatedSubQuestions.forEach(subQuestion => {
                    const subQuestionKey = `小题${subQuestion.number}`;
                    const subQuestionData = {
                        "题干坐标": subQuestion.coordinates,
                        "题干内容": subQuestion.attributes.content || "",
                        "题干印刷手写属性": subQuestion.attributes.printWriteAttribute || "印刷"
                    };

                    // 获取该小题的答题区域
                    const subQuestionAnswerAreas = answerAreas.filter(aa =>
                        aa.parentId === subQuestion.id ||
                        this.isAnnotationInside(aa, subQuestion)
                    );

                    if (subQuestionAnswerAreas.length > 0) {
                        subQuestionData["答题区域"] = {};

                        subQuestionAnswerAreas.forEach(answerArea => {
                            const answerAreaKey = `答题区域${answerArea.number}`;
                            subQuestionData["答题区域"][answerAreaKey] = {
                                "答题区域坐标": answerArea.coordinates,
                                "答题区域内容": answerArea.attributes.answerContent || "",
                                "答题区域印刷手写属性": answerArea.attributes.printWriteAttribute || "手写",
                                "批改结果": answerArea.attributes.gradeResult || "正确",
                                "正确答案": answerArea.attributes.correctAnswer || "",
                                "答案解析": answerArea.attributes.answerExplanation || ""
                            };
                        });
                }

                    jsonData[mainQuestionKey]["小题"][subQuestionKey] = subQuestionData;
                });
            }
        });

        return jsonData;
    }

    /**
     * 判断一个标注是否在另一个标注内部
     * @param {Object} inner 内部标注
     * @param {Object} outer 外部标注
     * @returns {boolean} 是否在内部
     */
    isAnnotationInside(inner, outer) {
        if (!inner.coordinates || !outer.coordinates) return false;

        const [innerStart, innerEnd] = inner.coordinates;
        const [outerStart, outerEnd] = outer.coordinates;

        const innerRect = {
            x: Math.min(innerStart[0], innerEnd[0]),
            y: Math.min(innerStart[1], innerEnd[1]),
            width: Math.abs(innerEnd[0] - innerStart[0]),
            height: Math.abs(innerEnd[1] - innerStart[1])
        };

        const outerRect = {
            x: Math.min(outerStart[0], outerEnd[0]),
            y: Math.min(outerStart[1], outerEnd[1]),
            width: Math.abs(outerEnd[0] - outerStart[0]),
            height: Math.abs(outerEnd[1] - outerStart[1])
        };

        return innerRect.x >= outerRect.x &&
               innerRect.y >= outerRect.y &&
               innerRect.x + innerRect.width <= outerRect.x + outerRect.width &&
               innerRect.y + innerRect.height <= outerRect.y + outerRect.height;
    }

    /**
     * 将JSON数据解析为标注对象数组
     * @param {Object} jsonData JSON数据
     * @returns {Array} 标注对象数组
     */
    parseJSONToAnnotations(jsonData) {
        const annotations = [];

        try {
            for (const [mainQuestionKey, mainQuestionData] of Object.entries(jsonData)) {
                // 解析大题编号
                const mainQuestionMatch = mainQuestionKey.match(/大题(\d+)/);
                if (!mainQuestionMatch) continue;

                const mainQuestionNumber = parseInt(mainQuestionMatch[1]);

                // 创建大题标注
                const mainQuestionAnnotation = {
                    id: Utils.generateId(),
                    type: 'main-question',
                    number: mainQuestionNumber,
                    coordinates: mainQuestionData.坐标,
                    attributes: {
                        content: mainQuestionData.题干文字 || '',
                        questionType: mainQuestionData.题型 || '填空题',
                        hasImage: mainQuestionData.题目是否带配图 === '是'
                    }
                };
                annotations.push(mainQuestionAnnotation);

                // 解析配图区域
                if (mainQuestionData.配图区域坐标) {
                    let imageAreaNumber = 1;
                    for (const [imageKey, imageCoords] of Object.entries(mainQuestionData.配图区域坐标)) {
                        const imageAnnotation = {
                            id: Utils.generateId(),
                            type: 'image-area',
                            number: imageAreaNumber++,
                            coordinates: imageCoords,
                            parentId: mainQuestionAnnotation.id,
                            attributes: {
                                content: imageKey
                            }
                        };
                        annotations.push(imageAnnotation);
                    }
                }

                // 解析小题
                if (mainQuestionData.小题) {
                    for (const [subQuestionKey, subQuestionData] of Object.entries(mainQuestionData.小题)) {
                        const subQuestionMatch = subQuestionKey.match(/小题(\d+)/);
                        if (!subQuestionMatch) continue;

                        const subQuestionNumber = parseInt(subQuestionMatch[1]);

                        // 创建小题标注
                        const subQuestionAnnotation = {
                            id: Utils.generateId(),
                            type: 'sub-question',
                            number: subQuestionNumber,
                            coordinates: subQuestionData.题干坐标,
                            parentId: mainQuestionAnnotation.id,
                            attributes: {
                                content: subQuestionData.题干内容 || '',
                                printWriteAttribute: subQuestionData.题干印刷手写属性 || '印刷'
                            }
                        };
                        annotations.push(subQuestionAnnotation);

                        // 解析答题区域
                        if (subQuestionData.答题区域) {
                            for (const [answerKey, answerData] of Object.entries(subQuestionData.答题区域)) {
                                const answerMatch = answerKey.match(/答题区域(\d+)/);
                                if (!answerMatch) continue;

                                const answerNumber = parseInt(answerMatch[1]);

                                // 创建答题区域标注
                                const answerAnnotation = {
                                    id: Utils.generateId(),
                                    type: 'answer-area',
                                    number: answerNumber,
                                    coordinates: answerData.答题区域坐标,
                                    parentId: subQuestionAnnotation.id,
                                    attributes: {
                                        answerContent: answerData.答题区域内容 || '',
                                        printWriteAttribute: answerData.答题区域印刷手写属性 || '手写',
                                        gradeResult: answerData.批改结果 || '正确',
                                        correctAnswer: answerData.正确答案 || '',
                                        answerExplanation: answerData.答案解析 || ''
                                    }
                                };
                                annotations.push(answerAnnotation);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('解析JSON数据失败:', error);
        }

        return annotations;
    }

    /**
     * 从JSON数据解析标注
     * @param {Object} jsonData JSON数据
     * @returns {Array} 标注数组
     */
    parseAnnotationsFromJSON(jsonData) {
        const annotations = [];
        let annotationId = 1;

        for (const [mainQuestionKey, mainQuestionData] of Object.entries(jsonData)) {
            // 解析大题
            const mainQuestionNumber = parseInt(mainQuestionKey.replace('大题', ''));
            const mainQuestion = {
                id: `main_${annotationId++}`,
                type: 'main-question',
                number: mainQuestionNumber,
                coordinates: mainQuestionData["坐标"],
                attributes: {
                    content: mainQuestionData["题干文字"] || "",
                    questionType: mainQuestionData["题型"] || "填空题",
                    hasImage: mainQuestionData["题目是否带配图"] === "是",
                    printWriteAttribute: "印刷"
                },
                parentId: null,
                children: [],
                selected: false,
                created: Date.now(),
                updated: Date.now()
            };
            annotations.push(mainQuestion);

            // 解析配图区域
            if (mainQuestionData["配图区域坐标"]) {
                for (const [imageKey, imageCoords] of Object.entries(mainQuestionData["配图区域坐标"])) {
                    const imageNumber = parseInt(imageKey.replace('配图', ''));
                    const imageArea = {
                        id: `image_${annotationId++}`,
                        type: 'image-area',
                        number: imageNumber,
                        coordinates: imageCoords,
                        attributes: {
                            content: "",
                            imageDescription: "",
                            printWriteAttribute: "印刷"
                        },
                        parentId: mainQuestion.id,
                        children: [],
                        selected: false,
                        created: Date.now(),
                        updated: Date.now()
                    };
                    annotations.push(imageArea);
                    mainQuestion.children.push(imageArea.id);
                }
            }

            // 解析小题
            if (mainQuestionData["小题"]) {
                for (const [subQuestionKey, subQuestionData] of Object.entries(mainQuestionData["小题"])) {
                    const subQuestionNumber = parseInt(subQuestionKey.replace('小题', ''));
                    const subQuestion = {
                        id: `sub_${annotationId++}`,
                        type: 'sub-question',
                        number: subQuestionNumber,
                        coordinates: subQuestionData["题干坐标"],
                        attributes: {
                            content: subQuestionData["题干内容"] || "",
                            printWriteAttribute: subQuestionData["题干印刷手写属性"] || "印刷"
                        },
                        parentId: mainQuestion.id,
                        children: [],
                        selected: false,
                        created: Date.now(),
                        updated: Date.now()
                    };
                    annotations.push(subQuestion);
                    mainQuestion.children.push(subQuestion.id);

                    // 解析答题区域
                    if (subQuestionData["答题区域"]) {
                        for (const [answerAreaKey, answerAreaData] of Object.entries(subQuestionData["答题区域"])) {
                            const answerAreaNumber = parseInt(answerAreaKey.replace('答题区域', ''));
                            const answerArea = {
                                id: `answer_${annotationId++}`,
                                type: 'answer-area',
                                number: answerAreaNumber,
                                coordinates: answerAreaData["答题区域坐标"],
                                attributes: {
                                    content: answerAreaData["答题区域内容"] || "",
                                    answerContent: answerAreaData["答题区域内容"] || "",
                                    printWriteAttribute: answerAreaData["答题区域印刷手写属性"] || "手写",
                                    gradeResult: answerAreaData["批改结果"] || "正确",
                                    correctAnswer: answerAreaData["正确答案"] || "",
                                    answerExplanation: answerAreaData["答案解析"] || ""
                                },
                                parentId: subQuestion.id,
                                children: [],
                                selected: false,
                                created: Date.now(),
                                updated: Date.now()
                            };
                            annotations.push(answerArea);
                            subQuestion.children.push(answerArea.id);
                        }
                    }
                }
            }
        }

        return annotations;
    }

    /**
     * 验证JSON数据格式
     * @param {Object} jsonData JSON数据
     * @returns {Object} 验证结果
     */
    validateJSONData(jsonData) {
        const errors = [];
        const warnings = [];

        if (!jsonData || typeof jsonData !== 'object') {
            errors.push('JSON数据格式无效');
            return { valid: false, errors, warnings };
        }

        const mainQuestionKeys = Object.keys(jsonData).filter(key => key.startsWith('大题'));
        if (mainQuestionKeys.length === 0) {
            errors.push('缺少大题数据');
        }

        for (const [mainQuestionKey, mainQuestionData] of Object.entries(jsonData)) {
            if (!mainQuestionKey.startsWith('大题')) continue;

            // 检查必需字段
            const requiredFields = ['坐标', '题型', '题干文字', '题目是否带配图'];
            for (const field of requiredFields) {
                if (!(field in mainQuestionData)) {
                    errors.push(`${mainQuestionKey}缺少字段: ${field}`);
                }
            }

            // 检查坐标格式
            if (mainQuestionData['坐标'] && !Utils.isValidCoordinates(mainQuestionData['坐标'])) {
                errors.push(`${mainQuestionKey}的坐标格式无效`);
            }

            // 检查小题
            if (mainQuestionData['小题']) {
                for (const [subQuestionKey, subQuestionData] of Object.entries(mainQuestionData['小题'])) {
                    if (!subQuestionData['题干坐标']) {
                        errors.push(`${subQuestionKey}缺少题干坐标`);
                    } else if (!Utils.isValidCoordinates(subQuestionData['题干坐标'])) {
                        errors.push(`${subQuestionKey}的题干坐标格式无效`);
                    }

                    if (!subQuestionData['题干内容']) {
                        warnings.push(`${subQuestionKey}的题干内容为空`);
                    }

                    // 检查答题区域
                    if (subQuestionData['答题区域']) {
                        for (const [answerAreaKey, answerAreaData] of Object.entries(subQuestionData['答题区域'])) {
                            if (!answerAreaData['答题区域坐标']) {
                                errors.push(`${answerAreaKey}缺少坐标`);
                            } else if (!Utils.isValidCoordinates(answerAreaData['答题区域坐标'])) {
                                errors.push(`${answerAreaKey}的坐标格式无效`);
                            }

                            if (!answerAreaData['答题区域内容']) {
                                warnings.push(`${answerAreaKey}的内容为空`);
                            }
                        }
                    }
                }
            }

            // 检查配图区域
            if (mainQuestionData['配图区域坐标']) {
                for (const [imageKey, imageCoords] of Object.entries(mainQuestionData['配图区域坐标'])) {
                    if (!Utils.isValidCoordinates(imageCoords)) {
                        errors.push(`${imageKey}的坐标格式无效`);
                    }
                }
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    }

    /**
     * 保存数据到本地存储
     * @param {Object} data 要保存的数据
     */
    saveToLocalStorage(data = null) {
        try {
            const dataToSave = data || this.currentData;
            if (dataToSave) {
                const saveData = {
                    data: dataToSave,
                    timestamp: Date.now(),
                    version: '1.0'
                };
                Utils.storage.save(this.storageKey, saveData);
                this.trigger('onDataSave', dataToSave);
            }
        } catch (error) {
            console.error('保存到本地存储失败:', error);
            Utils.showNotification('保存失败', 'error');
        }
    }

    /**
     * 从本地存储加载数据
     * @returns {Object|null} 加载的数据
     */
    loadFromLocalStorage() {
        try {
            const saveData = Utils.storage.load(this.storageKey);
            if (saveData && saveData.data) {
                this.currentData = saveData.data;
                this.trigger('onDataLoad', this.currentData);
                return this.currentData;
            }
        } catch (error) {
            console.error('从本地存储加载失败:', error);
            Utils.showNotification('加载失败', 'error');
        }
        return null;
    }

    /**
     * 导出JSON文件
     * @param {Object} jsonData JSON数据
     * @param {string} filename 文件名
     * @param {boolean} download 是否触发下载，默认true
     */
    exportJSON(jsonData, filename, download = true) {
        try {
            const jsonString = JSON.stringify(jsonData, null, 2);
            if (download) {
                Utils.file.download(jsonString, filename, 'application/json');
            }
            this.trigger('onDataExport', { data: jsonData, filename, jsonString });
            if (download) {
                Utils.showNotification('导出成功', 'success');
            }
            return jsonString;
        } catch (error) {
            console.error('导出JSON失败:', error);
            Utils.showNotification('导出失败', 'error');
            throw error;
        }
    }

    /**
     * 导入JSON文件
     * @param {File} file JSON文件
     * @returns {Promise<Object>} 导入的数据
     */
    async importJSON(file) {
        try {
            const content = await Utils.file.readAsText(file);
            const jsonData = JSON.parse(content);
            
            // 验证数据格式
            const validation = this.validateJSONData(jsonData);
            if (!validation.valid) {
                throw new Error('JSON格式验证失败: ' + validation.errors.join(', '));
            }

            if (validation.warnings.length > 0) {
                console.warn('JSON数据警告:', validation.warnings);
                Utils.showNotification(`导入成功，但有${validation.warnings.length}个警告`, 'warning');
            } else {
                Utils.showNotification('导入成功', 'success');
            }

            this.currentData = jsonData;
            this.trigger('onDataImport', jsonData);
            return jsonData;
        } catch (error) {
            console.error('导入JSON失败:', error);
            Utils.showNotification('导入失败: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * 清空当前数据
     */
    clearData() {
        this.currentData = null;
        Utils.storage.remove(this.storageKey);
    }

    /**
     * 获取当前数据
     * @returns {Object|null} 当前数据
     */
    getCurrentData() {
        return this.currentData;
    }

    /**
     * 设置当前数据
     * @param {Object} data 数据
     */
    setCurrentData(data) {
        this.currentData = data;
        this.saveToLocalStorage();
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopAutoSave();
        this.callbacks = {
            onDataLoad: [],
            onDataSave: [],
            onDataExport: [],
            onDataImport: []
        };
    }
}

// 导出数据管理器类
window.DataManager = DataManager;
