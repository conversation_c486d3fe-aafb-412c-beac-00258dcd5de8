{"name": "ocr-annotation-tool", "version": "1.0.1", "description": "OCR标注工具 - 专业的图片标注系统", "main": "electron/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["ocr", "annotation", "electron", "image-processing", "machine-learning"], "author": "OCR Annotation Tool Team", "license": "MIT", "devDependencies": {"cross-env": "^7.0.3", "electron": "^21.4.4", "electron-builder": "^23.6.0"}, "dependencies": {"fs-extra": "^10.1.0", "node-fetch": "^2.7.0"}, "build": {"appId": "com.ocrtools.annotation", "productName": "OCR标注工具", "directories": {"output": "dist-electron"}, "files": ["electron/**/*", "js/**/*", "css/**/*", "index.html", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}