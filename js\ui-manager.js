/**
 * OCR标注工具 - UI管理器
 * 负责用户界面的更新和交互逻辑
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.currentMode = 'annotation';
        this.selectedAnnotation = null;
        this.tempChildAnnotations = [];

        this.isUpdatingForm = false; // 标志是否正在更新表单

        this.initializeElements();
        this.setupEventListeners();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 工具按钮
        this.elements.toolButtons = {
            selectMainQuestion: document.getElementById('selectMainQuestion'),
            selectQuestionText: document.getElementById('selectQuestionText'),
            selectSubQuestion: document.getElementById('selectSubQuestion'),
            selectAnswerArea: document.getElementById('selectAnswerArea'),
            selectImageArea: document.getElementById('selectImageArea'),
            clearSelection: document.getElementById('clearSelection')
        };

        // 表单元素（已移除左侧面板的大题信息表单）
        this.elements.forms = {};

        // 操作按钮
        this.elements.actions = {
            saveData: document.getElementById('saveData'),
            loadData: document.getElementById('loadData'),
            clearAll: document.getElementById('clearAll'),
            exportData: document.getElementById('exportData')
        };

        // 信息面板
        this.elements.panels = {
            selectedAnnotationInfo: document.getElementById('selectedAnnotationInfo'),
            hierarchySection: document.getElementById('hierarchySection'),
            hierarchyTitle: document.getElementById('hierarchyTitle'),
            hierarchyContent: document.getElementById('hierarchyContent'),
            qualityCheckInfo: document.getElementById('qualityCheckInfo'),
            currentParentHint: document.getElementById('currentParentHint'),
            currentParentName: document.getElementById('currentParentName')
        };

        // 模式选择器
        this.elements.modeSelector = document.getElementById('modeSelector');

        // 标注显示开关
        this.elements.toggleAnnotations = document.getElementById('toggleAnnotations');

        // 快捷键按钮
        this.elements.showShortcuts = document.getElementById('showShortcuts');

        // 题目计数器
        this.elements.questionCounter = document.getElementById('questionCounter');
        this.elements.currentQuestionCount = document.getElementById('currentQuestionCount');



        // 工作区相关按钮 - 简化为三个核心功能
        this.elements.openWorkspace = document.getElementById('openWorkspace');
        this.elements.saveCurrentToWorkspace = document.getElementById('saveCurrentToWorkspace');
        this.elements.clearAll = document.getElementById('clearAll');

        // 工作区状态元素
        this.elements.workspaceStatus = document.getElementById('workspaceStatus');
        this.elements.workspaceName = document.getElementById('workspaceName');
        this.elements.imageFolderStatus = document.getElementById('imageFolderStatus');
        this.elements.jsonFolderStatus = document.getElementById('jsonFolderStatus');
        this.elements.imageCount = document.getElementById('imageCount');
        this.elements.jsonCount = document.getElementById('jsonCount');
        this.elements.questionNavigation = document.getElementById('questionNavigation');

        // OCR相关元素
        this.elements.ocr = {
            section: document.getElementById('ocrSection'),
            cancelButton: document.getElementById('cancelOCR'),
            progress: document.getElementById('ocrProgress'),
            progressText: document.getElementById('ocrProgressText'),
            progressPercent: document.getElementById('ocrProgressPercent'),
            progressFill: document.getElementById('ocrProgressFill'),
            results: document.getElementById('ocrResults'),
            resultsList: document.getElementById('ocrResultsList')
        };

        // 设置相关元素
        this.elements.settings = {
            showButton: document.getElementById('showSettings'),
            modal: document.getElementById('settingsModal'),
            model: document.getElementById('settingsOcrModel'),
            useTrueBatch: document.getElementById('settingsUseTrueBatch'),
            tabs: document.querySelectorAll('.settings-tab'),
            panels: document.querySelectorAll('.settings-panel')
        };
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 工具按钮事件
        Object.entries(this.elements.toolButtons).forEach(([key, button]) => {
            if (button) {
                button.addEventListener('click', (e) => {
                    this.onToolButtonClick(e.target.dataset.tool || key);
                });
            }
        });

        // 表单事件（已移除左侧面板的大题信息表单事件）

        // 模式选择器事件
        if (this.elements.modeSelector) {
            this.elements.modeSelector.addEventListener('change', (e) => {
                this.onModeChange(e.target.value);
            });
        }

        // 标注显示开关事件
        if (this.elements.toggleAnnotations) {
            this.elements.toggleAnnotations.addEventListener('click', () => {
                this.onToggleAnnotations();
            });
        }

        // 快捷键按钮事件
        if (this.elements.showShortcuts) {
            this.elements.showShortcuts.addEventListener('click', () => {
                Utils.showShortcutsHelp();
            });
        }

        // 设置按钮事件
        if (this.elements.settings.showButton) {
            this.elements.settings.showButton.addEventListener('click', () => {
                this.showSettings();
            });
        }

        // 设置标签页切换事件
        this.elements.settings.tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchSettingsTab(e.target.dataset.tab);
            });
        });



        // 工作区按钮事件 - 简化为三个核心功能
        if (this.elements.openWorkspace) {
            this.elements.openWorkspace.addEventListener('click', () => {
                this.openWorkspace();
            });
        }



        if (this.elements.clearAll) {
            this.elements.clearAll.addEventListener('click', () => {
                this.clearCurrentAnnotations();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.onKeyDown(e);
        });

        // OCR事件监听器
        this.setupOCREventListeners();

        // Electron菜单事件监听 - 简化
        if (window.isElectron && window.electronAPI) {
            window.electronAPI.onMenuOpenWorkspace(() => {
                this.openWorkspace();
            });
        }
    }

    /**
     * 工具按钮点击事件
     * @param {string} tool 工具类型
     */
    onToolButtonClick(tool) {
        // 更新按钮状态
        this.updateToolButtonStates(tool === 'clearSelection' ? '' : tool);
        
        // 触发自定义事件
        this.dispatchEvent('toolSelect', { tool: tool === 'clearSelection' ? '' : tool });
    }

    /**
     * 更新工具按钮状态
     * @param {string} activeTool 激活的工具
     */
    updateToolButtonStates(activeTool) {
        Object.entries(this.elements.toolButtons).forEach(([key, button]) => {
            if (button) {
                const tool = button.dataset.tool || key;
                button.classList.toggle('active', tool === activeTool);
            }
        });
    }

    // 移除了左侧面板大题信息表单的事件处理方法
    // 大题信息现在只在右侧编辑表单中处理

    /**
     * 模式改变事件
     * @param {string} mode 模式
     */
    onModeChange(mode) {
        this.currentMode = mode;
        this.dispatchEvent('modeChange', { mode });
    }

    /**
     * 键盘事件处理
     * @param {KeyboardEvent} e 键盘事件
     */
    onKeyDown(e) {
        // 防止在输入框中触发快捷键
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        const shortcuts = {
            '1': 'main-question',
            '2': 'question-text',
            '3': 'sub-question',
            '4': 'answer-area',
            '5': 'image-area',
            'ArrowLeft': 'prevImage',
            'ArrowRight': 'nextImage',
            '=': 'zoomIn',
            '+': 'zoomIn',
            '-': 'zoomOut',
            '0': 'resetZoom',
            'q': 'toggleAnnotations',
            'Q': 'toggleAnnotations',
            'w': 'toggleQualityCheck',
            'W': 'toggleQualityCheck',
            'd': 'deleteSelected',
            'D': 'deleteSelected',
            'F1': 'showShortcuts',
            'Escape': 'clearSelection',
            'Delete': 'deleteSelected'
        };

        let action = shortcuts[e.key];
        
        // Ctrl组合键
        if (e.ctrlKey) {
            switch (e.key) {
                case 's':
                    e.preventDefault();
                    action = 'save';
                    break;
                case 'o':
                    e.preventDefault();
                    action = 'load';
                    break;
                case 'e':
                    e.preventDefault();
                    action = 'export';
                    break;
            }
        }

        if (action) {
            e.preventDefault();
            this.dispatchEvent('shortcut', { action, key: e.key, ctrlKey: e.ctrlKey });
        }
    }

    /**
     * 切换标注显示状态
     */
    onToggleAnnotations() {
        this.dispatchEvent('toggleAnnotations');
    }

    /**
     * 更新标注显示开关按钮状态
     * @param {boolean} visible 是否显示标注
     */
    updateToggleAnnotationsButton(visible) {
        const button = this.elements.toggleAnnotations;
        if (!button) return;

        const text = button.querySelector('span');

        if (visible) {
            button.classList.add('active');
            button.classList.remove('inactive');
            if (text) text.textContent = '显示标注';
            button.title = '隐藏标注 (V)';
        } else {
            button.classList.remove('active');
            button.classList.add('inactive');
            if (text) text.textContent = '隐藏标注';
            button.title = '显示标注 (V)';
        }
    }

    /**
     * 获取指定类型可以包含的子类型
     * @param {string} parentType 父级类型
     * @returns {Array} 子类型数组
     */
    getChildTypes(parentType) {
        const childTypes = [];
        const annotationTypes = {
            'main-question': { parent: null },
            'sub-question': { parent: 'main-question' },
            'answer-area': { parent: 'sub-question' },
            'image-area': { parent: 'main-question' }
        };

        for (const [type, config] of Object.entries(annotationTypes)) {
            if (config.parent === parentType) {
                childTypes.push(type);
            }
        }

        return childTypes;
    }

    /**
     * 更新父级提示显示
     * @param {Object|null} annotation 当前选中的标注
     */
    updateParentHint(annotation) {
        const hintPanel = this.elements.panels.currentParentHint;
        const nameElement = this.elements.panels.currentParentName;

        if (!hintPanel || !nameElement) return;

        if (!annotation) {
            hintPanel.style.display = 'none';
            return;
        }

        // 检查是否可以作为父级
        const canBeParent = this.getChildTypes(annotation.type).length > 0;

        if (canBeParent) {
            const typeNames = {
                'main-question': '大题',
                'question-text': '题干文字',
                'sub-question': '小题',
                'answer-area': '答题区域',
                'image-area': '配图区域'
            };

            const typeName = typeNames[annotation.type] || annotation.type;
            const displayName = (annotation.type === 'question-text' || annotation.number === null) ?
                typeName : `${typeName}${annotation.number}`;
            nameElement.textContent = displayName;
            hintPanel.style.display = 'block';
        } else {
            hintPanel.style.display = 'none';
        }
    }

    /**
     * 更新选中标注信息显示
     * @param {Object|null} annotation 标注对象
     */
    updateSelectedAnnotationInfo(annotation) {
        this.selectedAnnotation = annotation;
        const infoPanel = this.elements.panels.selectedAnnotationInfo;

        if (!infoPanel) return;

        // 设置更新标志，避免在表单重建时触发自动保存
        this.isUpdatingForm = true;

        // 更新父级提示
        this.updateParentHint(annotation);

        if (!annotation) {
            infoPanel.innerHTML = '<p class="no-selection">请选择一个标注区域</p>';
            this.updateNavigationSelection(null);
            this.isUpdatingForm = false;
            return;
        }

        // 同步更新导航选中状态
        this.updateNavigationSelection(annotation.id);

        const typeNames = {
            'main-question': '大题',
            'question-text': '题干文字',
            'sub-question': '小题',
            'answer-area': '答题区域',
            'image-area': '配图区域'
        };

        const typeName = typeNames[annotation.type] || annotation.type;
        const coordinates = Utils.formatCoordinates(annotation.coordinates);

        // 题干文字框不显示编号
        const displayName = (annotation.type === 'question-text' || annotation.number === null) ?
            typeName : `${typeName}${annotation.number}`;

        let formHTML = `
            <div class="annotation-item ${annotation.type} selected">
                <div class="annotation-type">${displayName}</div>
                <div class="annotation-coords">${coordinates}</div>
            </div>
            <div class="edit-form annotation-edit-form">
        `;

        // 根据标注类型生成不同的编辑表单
        switch (annotation.type) {
            case 'main-question':
                // 获取该大题下的题干文字框
                const questionTexts = window.ocrTool.annotationManager.getAllAnnotations().filter(ann =>
                    ann.type === 'question-text' && ann.parentId === annotation.id
                ).sort((a, b) => a.number - b.number);


                // 优先使用题干文字框的内容，如果没有则使用大题自身的content
                const questionTextContent = questionTexts.length > 0 ?
                    (questionTexts[0].attributes.content || '') :
                    (annotation.attributes.content || '');
                const questionTextPrintWrite = questionTexts.length > 0 ?
                    (questionTexts[0].attributes.printWriteAttribute || '印刷') : '印刷';

                formHTML += `
                    <div class="form-group">
                        <label>题型：</label>
                        <select id="editQuestionType">
                            <option value="填空题" ${annotation.attributes.questionType === '填空题' ? 'selected' : ''}>填空题</option>
                            <option value="选择题" ${annotation.attributes.questionType === '选择题' ? 'selected' : ''}>选择题</option>
                            <option value="解答题" ${annotation.attributes.questionType === '解答题' ? 'selected' : ''}>解答题</option>
                            <option value="判断题" ${annotation.attributes.questionType === '判断题' ? 'selected' : ''}>判断题</option>
                            <option value="其他" ${annotation.attributes.questionType === '其他' ? 'selected' : ''}>其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>题干文字：</label>
                        <textarea id="editContent" placeholder="输入题干内容...">${questionTextContent}</textarea>
                    </div>
                    <div class="form-group">
                        <label>印刷手写属性：</label>
                        <select id="editPrintWriteAttribute">
                            <option value="印刷" ${questionTextPrintWrite === '印刷' ? 'selected' : ''}>印刷</option>
                            <option value="手写" ${questionTextPrintWrite === '手写' ? 'selected' : ''}>手写</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="editHasImage" ${annotation.attributes.hasImage ? 'checked' : ''}>
                            题目带配图
                        </label>
                    </div>
                `;
                break;

            case 'question-text':
                // 题干文字框使用与大题相同的编辑界面
                const parentAnnotation = window.ocrTool.annotationManager.getAllAnnotations().find(ann => ann.id === annotation.parentId);
                if (parentAnnotation) {
                    // 递归调用，显示父级大题的编辑表单
                    return this.updateSelectedAnnotationInfo(parentAnnotation);
                }
                break;

            case 'sub-question':
                formHTML += `
                    <div class="form-group">
                        <label>题干内容：</label>
                        <textarea id="editContent" placeholder="输入小题内容...">${annotation.attributes.content || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label>印刷手写属性：</label>
                        <select id="editPrintWriteAttribute">
                            <option value="印刷" ${annotation.attributes.printWriteAttribute === '印刷' ? 'selected' : ''}>印刷</option>
                            <option value="手写" ${annotation.attributes.printWriteAttribute === '手写' ? 'selected' : ''}>手写</option>
                        </select>
                    </div>
                `;
                break;

            case 'answer-area':
                formHTML += `
                    <div class="form-group">
                        <label>答题区域内容：</label>
                        <textarea id="editAnswerContent" placeholder="输入学生答案...">${annotation.attributes.answerContent || ''}</textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>印刷手写属性：</label>
                            <select id="editPrintWriteAttribute">
                                <option value="印刷" ${annotation.attributes.printWriteAttribute === '印刷' ? 'selected' : ''}>印刷</option>
                                <option value="手写" ${annotation.attributes.printWriteAttribute === '手写' ? 'selected' : ''}>手写</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>批改结果：</label>
                            <select id="editGradeResult">
                                <option value="正确" ${annotation.attributes.gradeResult === '正确' ? 'selected' : ''}>正确</option>
                                <option value="错误" ${annotation.attributes.gradeResult === '错误' ? 'selected' : ''}>错误</option>
                                <option value="部分正确" ${annotation.attributes.gradeResult === '部分正确' ? 'selected' : ''}>部分正确</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>正确答案：</label>
                        <textarea id="editCorrectAnswer" placeholder="输入正确答案...">${annotation.attributes.correctAnswer || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label>答案解析：</label>
                        <textarea id="editAnswerExplanation" placeholder="输入答案解析...">${annotation.attributes.answerExplanation || ''}</textarea>
                    </div>
                `;
                break;

            case 'image-area':
                formHTML += `
                    <div class="form-group">
                        <label>配图描述：</label>
                        <textarea id="editImageDescription" placeholder="输入配图描述...">${annotation.attributes.imageDescription || ''}</textarea>
                    </div>
                `;
                break;
        }

        // 根据标注类型添加特定的按钮
        let actionButtons = '';
        if (annotation.type === 'sub-question') {
            actionButtons += `<button type="button" class="btn btn-primary" onclick="uiManager.replaceAnswerAreaPlaceholders('${annotation.id}')">替换占位符</button>`;
        }
        actionButtons += `<button type="button" class="btn btn-danger" onclick="uiManager.deleteAnnotation()">删除</button>`;

        formHTML += `
                <div class="form-actions">
                    ${actionButtons}
                </div>
            </div>
        `;

        infoPanel.innerHTML = formHTML;

        // 为表单字段添加自动保存事件监听器
        this.setupAutoSaveListeners(annotation);

        // 更新层级内容显示
        this.updateHierarchyContent(annotation);

        // 显示或隐藏OCR面板
        this.showOCRPanel(annotation);

        // 重置更新标志
        this.isUpdatingForm = false;
    }

    /**
     * 为表单字段设置实时更新监听器
     * @param {Object} annotation 标注对象
     */
    setupAutoSaveListeners(annotation) {
        const form = document.querySelector('.edit-form, .annotation-edit-form');
        if (!form) return;

        // 获取标注管理器中的实际标注对象引用
        const actualAnnotation = window.ocrTool?.annotationManager?.annotations?.get(annotation.id);
        if (!actualAnnotation) {
            console.warn('无法找到标注对象:', annotation.id);
            return;
        }

        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            // 添加输入事件监听器，实现实时更新
            const updateHandler = () => {
                this.updateAnnotationInMemory(actualAnnotation);
            };

            // 对于不同类型的输入使用不同的事件
            if (input.type === 'text' || input.tagName === 'TEXTAREA') {
                input.addEventListener('input', updateHandler);
                input.addEventListener('blur', updateHandler); // 失去焦点时也更新
            } else if (input.tagName === 'SELECT' || input.type === 'checkbox') {
                input.addEventListener('change', updateHandler);
            }
        });
    }

    /**
     * 实时更新标注数据到内存（无需保存按钮）
     * @param {Object} annotation 标注对象（标注管理器中的实际引用）
     */
    updateAnnotationInMemory(annotation) {
        if (!annotation || this.isUpdatingForm) return;

        const updates = this.collectFormData(annotation);
        if (Object.keys(updates).length === 0) return;

        // 直接更新标注对象的属性
        Object.assign(annotation.attributes, updates);
        annotation.updated = Date.now();

        // 立即保存到内存
        if (window.ocrTool) {
            window.ocrTool.saveCurrentAnnotations();
        }

        // 更新导航显示（如果标注内容影响导航显示）
        if (window.ocrTool) {
            window.ocrTool.updateAnnotationLists();
        }
    }

    /**
     * 兼容性方法：自动保存标注编辑（保留旧接口）
     * @param {Object} annotation 标注对象
     */
    autoSaveAnnotationEdit(annotation) {
        // 获取标注管理器中的实际标注对象引用
        const actualAnnotation = window.ocrTool?.annotationManager?.annotations?.get(annotation.id);
        if (actualAnnotation) {
            this.updateAnnotationInMemory(actualAnnotation);
        }
    }

    /**
     * 收集表单数据
     * @param {Object} annotation 标注对象
     * @returns {Object} 更新数据
     */
    collectFormData(annotation) {
        const updates = {};

        // 根据标注类型收集表单数据
        switch (annotation.type) {
            case 'main-question':
                const questionType = document.getElementById('editQuestionType');
                const content = document.getElementById('editContent');
                const printWriteAttribute = document.getElementById('editPrintWriteAttribute');
                const hasImage = document.getElementById('editHasImage');

                if (questionType) updates.questionType = questionType.value;
                if (hasImage) updates.hasImage = hasImage.checked;

                // 处理题干文字的保存
                if (content || printWriteAttribute) {
                    const questionTexts = window.ocrTool.annotationManager.getAllAnnotations().filter(ann =>
                        ann.type === 'question-text' && ann.parentId === annotation.id
                    );

                    if (questionTexts.length > 0) {
                        // 如果有题干文字框，更新题干文字框
                        questionTexts.forEach(questionText => {
                            const questionTextUpdates = {};
                            if (content) questionTextUpdates.content = content.value;
                            if (printWriteAttribute) questionTextUpdates.printWriteAttribute = printWriteAttribute.value;

                            // 更新题干文字框的属性
                            Object.assign(questionText.attributes, questionTextUpdates);

                            // 触发更新事件
                            this.dispatchEvent('annotation-updated', {
                                annotation: questionText,
                                updates: questionTextUpdates
                            });
                        });
                    } else {
                        // 如果没有题干文字框，将题干文字保存到大题的content中
                        if (content) {
                            updates.content = content.value;
                        }
                    }
                }
                break;

            case 'question-text':
                // 题干文字框的编辑通过大题表单处理，这里不需要单独处理
                break;

            case 'sub-question':
                const subContent = document.getElementById('editContent');
                const subPrintWrite = document.getElementById('editPrintWriteAttribute');

                if (subContent) updates.content = subContent.value;
                if (subPrintWrite) updates.printWriteAttribute = subPrintWrite.value;
                break;

            case 'answer-area':
                const answerContent = document.getElementById('editAnswerContent');
                const answerPrintWrite = document.getElementById('editPrintWriteAttribute');
                const gradeResult = document.getElementById('editGradeResult');
                const correctAnswer = document.getElementById('editCorrectAnswer');
                const answerExplanation = document.getElementById('editAnswerExplanation');

                if (answerContent) updates.answerContent = answerContent.value;
                if (answerPrintWrite) updates.printWriteAttribute = answerPrintWrite.value;
                if (gradeResult) updates.gradeResult = gradeResult.value;
                if (correctAnswer) updates.correctAnswer = correctAnswer.value;
                if (answerExplanation) updates.answerExplanation = answerExplanation.value;
                break;

            case 'image-area':
                const imageDescription = document.getElementById('editImageDescription');
                if (imageDescription) updates.imageDescription = imageDescription.value;
                break;
        }

        return updates;
    }

    /**
     * 保存标注编辑（手动保存，保留兼容性）
     */
    saveAnnotationEdit() {
        if (!this.selectedAnnotation) return;

        // 获取标注管理器中的实际标注对象引用
        const actualAnnotation = window.ocrTool?.annotationManager?.annotations?.get(this.selectedAnnotation.id);
        if (actualAnnotation) {
            this.updateAnnotationInMemory(actualAnnotation);
            Utils.showNotification('标注更新成功', 'success');
        }
    }

    /**
     * 删除标注
     */
    deleteAnnotation() {
        if (!this.selectedAnnotation) return;

        Utils.showConfirm(
            `确定要删除这个${this.selectedAnnotation.type === 'main-question' ? '大题' :
                           this.selectedAnnotation.type === 'sub-question' ? '小题' :
                           this.selectedAnnotation.type === 'answer-area' ? '答题区域' : '配图区域'}吗？`,
            () => {
                this.dispatchEvent('annotationDelete', {
                    annotationId: this.selectedAnnotation.id
                });
                this.selectedAnnotation = null;
                this.updateSelectedAnnotationInfo(null);
            }
        );
    }

    /**
     * 替换小题题干中的答题区域文字为占位符
     * @param {string} subQuestionId 小题ID
     */
    replaceAnswerAreaPlaceholders(subQuestionId) {
        try {
            // 获取小题标注
            const subQuestion = window.ocrTool?.annotationManager?.getAnnotationById(subQuestionId);
            if (!subQuestion || subQuestion.type !== 'sub-question') {
                Utils.showNotification('找不到指定的小题', 'error');
                return;
            }

            // 获取小题的题干内容
            let content = subQuestion.attributes.content || '';
            if (!content.trim()) {
                Utils.showNotification('小题题干内容为空', 'warning');
                return;
            }

            // 获取该小题下的所有答题区域
            const allAnnotations = window.ocrTool?.annotationManager?.getAllAnnotations() || [];
            const answerAreas = allAnnotations.filter(ann =>
                ann.type === 'answer-area' && ann.parentId === subQuestionId
            ).sort((a, b) => a.number - b.number);

            if (answerAreas.length === 0) {
                Utils.showNotification('该小题没有答题区域', 'warning');
                return;
            }

            console.log('🔄 [占位符替换] 开始替换，小题内容:', content);
            console.log('🔄 [占位符替换] 答题区域列表:', answerAreas.map(a => ({
                number: a.number,
                content: a.attributes.answerContent || '(空)'
            })));

            let updatedContent = content;
            let replacementCount = 0;

            // 遍历答题区域，将其内容替换为占位符
            answerAreas.forEach(answerArea => {
                const answerContent = answerArea.attributes.answerContent;
                if (answerContent && answerContent.trim()) {
                    const placeholder = `{答题区域${answerArea.number}}`;

                    // 简单的文字替换
                    const beforeReplace = updatedContent;
                    updatedContent = updatedContent.replace(new RegExp(answerContent.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), placeholder);

                    if (beforeReplace !== updatedContent) {
                        replacementCount++;
                        console.log('🔄 [占位符替换] 替换成功:', answerContent, '->', placeholder);
                    } else {
                        console.log('🔄 [占位符替换] 未找到匹配文字:', answerContent);
                    }
                }
            });

            if (replacementCount > 0) {
                // 更新小题内容
                subQuestion.attributes.content = updatedContent;
                subQuestion.updated = Date.now();

                // 更新表单显示
                const contentTextarea = document.getElementById('editContent');
                if (contentTextarea) {
                    contentTextarea.value = updatedContent;
                }

                // 触发标注更新事件
                window.ocrTool?.annotationManager?.trigger('onAnnotationUpdate', subQuestion);

                console.log('🔄 [占位符替换] 替换完成，更新后内容:', updatedContent);
                Utils.showNotification(`成功替换 ${replacementCount} 个答题区域文字为占位符`, 'success');
            } else {
                Utils.showNotification('没有找到可替换的答题区域文字', 'warning');
            }

        } catch (error) {
            console.error('🔄 [占位符替换] 替换过程出错:', error);
            Utils.showNotification('替换过程出错: ' + error.message, 'error');
        }
    }

    /**
     * 更新小题列表显示
     * @param {Array} subQuestions 小题数组
     */
    updateSubQuestionsList(subQuestions) {
        const listPanel = this.elements.panels.subQuestionsList;
        if (!listPanel) return;

        if (subQuestions.length === 0) {
            listPanel.innerHTML = '<p class="empty-list">暂无小题</p>';
            return;
        }

        let html = '';
        subQuestions.forEach(subQuestion => {
            const coordinates = Utils.formatCoordinates(subQuestion.coordinates);
            html += `
                <div class="annotation-item sub-question" data-id="${subQuestion.id}">
                    <div class="annotation-type">小题${subQuestion.number}</div>
                    <div class="annotation-content">${subQuestion.attributes.content || '无内容'}</div>
                    <div class="annotation-coords">${coordinates}</div>
                </div>
            `;
        });

        listPanel.innerHTML = html;

        // 添加点击事件
        listPanel.querySelectorAll('.annotation-item').forEach(item => {
            item.addEventListener('click', () => {
                const annotationId = item.dataset.id;
                this.dispatchEvent('annotationSelect', { annotationId });
            });
        });
    }

    /**
     * 更新配图列表显示
     * @param {Array} imageAreas 配图数组
     */
    updateImageAreasList(imageAreas) {
        const listPanel = this.elements.panels.imageAreasList;
        if (!listPanel) return;

        if (imageAreas.length === 0) {
            listPanel.innerHTML = '<p class="empty-list">暂无配图</p>';
            return;
        }

        let html = '';
        imageAreas.forEach(imageArea => {
            const coordinates = Utils.formatCoordinates(imageArea.coordinates);
            html += `
                <div class="annotation-item image-area" data-id="${imageArea.id}">
                    <div class="annotation-type">配图${imageArea.number}</div>
                    <div class="annotation-content">${imageArea.attributes.imageDescription || '无描述'}</div>
                    <div class="annotation-coords">${coordinates}</div>
                </div>
            `;
        });

        listPanel.innerHTML = html;

        // 添加点击事件
        listPanel.querySelectorAll('.annotation-item').forEach(item => {
            item.addEventListener('click', () => {
                const annotationId = item.dataset.id;
                this.dispatchEvent('annotationSelect', { annotationId });
            });
        });
    }

    /**
     * 更新大题信息表单（已移除左侧面板表单，现在只在右侧编辑表单中处理）
     * @param {Object} _mainQuestion 大题对象（未使用）
     */
    updateMainQuestionForm(_mainQuestion) {
        // 左侧面板的大题信息表单已移除
        // 大题信息现在只在右侧面板的编辑表单中显示和编辑
        // 保留此方法以避免破坏现有调用，但不执行任何操作
    }

    /**
     * 设置模式
     * @param {string} mode 模式
     */
    setMode(mode) {
        this.currentMode = mode;
        if (this.elements.modeSelector) {
            this.elements.modeSelector.value = mode;
        }
    }

    /**
     * 获取当前模式
     * @returns {string} 当前模式
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * 更新题目计数器
     * @param {number} count 题目数量
     */
    updateQuestionCounter(count) {
        if (this.elements.currentQuestionCount) {
            this.elements.currentQuestionCount.textContent = count;
        }

        // 更新计数器的颜色和样式
        if (this.elements.questionCounter) {
            if (count === 0) {
                this.elements.questionCounter.style.background = 'rgba(108, 117, 125, 0.2)';
                this.elements.questionCounter.style.borderColor = 'rgba(108, 117, 125, 0.3)';
            } else {
                this.elements.questionCounter.style.background = 'rgba(46, 204, 113, 0.2)';
                this.elements.questionCounter.style.borderColor = 'rgba(46, 204, 113, 0.3)';
            }
        }
    }

    /**
     * 打开工作区
     */
    async openWorkspace() {
        if (window.ocrTool && window.ocrTool.workspaceManager) {
            const success = await window.ocrTool.workspaceManager.openWorkspace();
            if (success) {
                // 启用保存按钮
                if (this.elements.saveToWorkspace) {
                    this.elements.saveToWorkspace.disabled = false;
                }
            }
        } else {
            Utils.showNotification('工作区管理器未初始化', 'error');
        }
    }

    /**
     * 清除当前图片的所有标注
     */
    clearCurrentAnnotations() {
        if (!window.ocrTool || !window.ocrTool.annotationManager) {
            Utils.showNotification('标注管理器未初始化', 'error');
            return;
        }

        const annotations = window.ocrTool.annotationManager.getAllAnnotations();
        if (annotations.length === 0) {
            Utils.showNotification('当前图片没有标注', 'info');
            return;
        }

        Utils.showConfirm(
            `确定要清除当前图片的所有 ${annotations.length} 个标注吗？\n此操作不可撤销。`,
            () => {
                // 清除所有标注
                window.ocrTool.annotationManager.clear();

                // 更新UI
                this.updateSelectedAnnotationInfo(null);
                window.ocrTool.updateAnnotationLists();

                // 保存到内存（清空状态）
                window.ocrTool.saveCurrentAnnotations();

                Utils.showNotification('已清除当前图片的所有标注', 'success');
            }
        );
    }

    /**
     * 保存当前图片到工作区
     */
    async saveCurrentToWorkspace() {
        if (!window.ocrTool || !window.ocrTool.workspaceManager) {
            Utils.showNotification('工作区管理器未初始化', 'error');
            return;
        }

        const workspaceManager = window.ocrTool.workspaceManager;
        if (!workspaceManager.isWorkspaceLoaded) {
            Utils.showNotification('请先打开工作区', 'warning');
            return;
        }

        try {
            // 获取当前图片和标注数据
            const currentImage = window.ocrTool.imageManager.getCurrentImage();
            if (!currentImage) {
                Utils.showNotification('没有当前图片', 'warning');
                return;
            }

            const annotations = window.ocrTool.annotationManager.exportAnnotations();
            const mainQuestions = annotations.filter(ann => ann.type === 'main-question');

            if (mainQuestions.length === 0) {
                Utils.showNotification('没有大题标注，无法保存', 'warning');
                return;
            }

            // 为每个大题生成单独的JSON文件
            let savedCount = 0;
            for (const mainQuestion of mainQuestions) {
                const questionData = window.ocrTool.dataManager.generateJSONFromAnnotations(
                    currentImage,
                    annotations.filter(ann =>
                        ann.type === 'main-question' && ann.id === mainQuestion.id ||
                        ann.parentId === mainQuestion.id ||
                        window.ocrTool.dataManager.isAnnotationInside(ann, mainQuestion)
                    ),
                    {}
                );

                const success = await workspaceManager.saveJsonToWorkspace(
                    currentImage.name,
                    mainQuestion.number,
                    questionData
                );

                if (success) {
                    savedCount++;
                }
            }

            if (savedCount > 0) {
                Utils.showNotification(`成功保存 ${savedCount} 个JSON文件`, 'success');
            } else {
                Utils.showNotification('保存失败', 'error');
            }

        } catch (error) {
            console.error('保存到工作区失败:', error);
            Utils.showNotification('保存到工作区失败: ' + error.message, 'error');
        }
    }



    /**
     * 更新工作区状态
     * @param {Object} status 工作区状态
     */
    updateWorkspaceStatus(status) {
        if (!this.elements.workspaceStatus) return;

        if (status.isLoaded) {
            this.elements.workspaceStatus.style.display = 'block';

            if (this.elements.workspaceName) {
                this.elements.workspaceName.textContent = status.workspaceName || '未知';
            }

            if (this.elements.imageFolderStatus) {
                this.elements.imageFolderStatus.textContent = status.hasImageFolder ? '✅ 2、原始图片' : '❌ 未找到';
                this.elements.imageFolderStatus.style.color = status.hasImageFolder ? '#28a745' : '#dc3545';
            }

            if (this.elements.jsonFolderStatus) {
                this.elements.jsonFolderStatus.textContent = status.hasJsonFolder ? '✅ 1、交付json' : '❌ 未找到';
                this.elements.jsonFolderStatus.style.color = status.hasJsonFolder ? '#28a745' : '#dc3545';
            }

            if (this.elements.imageCount) {
                this.elements.imageCount.textContent = status.imageCount || 0;
            }

            if (this.elements.jsonCount) {
                this.elements.jsonCount.textContent = status.jsonFileCount || 0;
            }

            // 启用保存按钮（需要JSON文件夹存在）
            if (this.elements.saveToWorkspace) {
                this.elements.saveToWorkspace.disabled = !status.hasJsonFolder;
            }
        } else {
            this.elements.workspaceStatus.style.display = 'none';

            // 禁用保存按钮
            if (this.elements.saveToWorkspace) {
                this.elements.saveToWorkspace.disabled = true;
            }
        }
    }



    /**
     * 更新工作区按钮状态
     * @param {boolean} hasWorkspace 是否有工作区
     * @param {boolean} hasAnnotations 是否有标注
     */
    updateWorkspaceButtons(hasWorkspace, hasAnnotations) {
        // 保存按钮：需要有工作区且当前图片有标注
        if (this.elements.saveCurrentToWorkspace) {
            this.elements.saveCurrentToWorkspace.disabled = !hasWorkspace || !hasAnnotations;
        }

        // 清除按钮：只需要当前图片有标注
        if (this.elements.clearAll) {
            this.elements.clearAll.disabled = !hasAnnotations;
        }
    }

    /**
     * 显示/隐藏加载状态
     * @param {boolean} show 是否显示
     * @param {string} message 加载消息
     */
    showLoading(show, message = '加载中...') {
        // 使用通知系统显示加载状态
        if (show) {
            Utils.showNotification(message, 'info', 2000);
        }
    }

    /**
     * 派发自定义事件
     * @param {string} eventName 事件名称
     * @param {Object} detail 事件详情
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    /**
     * 更新层级内容显示
     * @param {Object|null} annotation 当前选中的标注
     */
    updateHierarchyContent(annotation) {
        const hierarchySection = this.elements.panels.hierarchySection;
        const hierarchyTitle = this.elements.panels.hierarchyTitle;
        const hierarchyContent = this.elements.panels.hierarchyContent;

        if (!hierarchySection || !hierarchyTitle || !hierarchyContent) return;

        if (!annotation) {
            hierarchySection.style.display = 'none';
            return;
        }

        // 根据标注类型显示不同的层级内容
        switch (annotation.type) {
            case 'main-question':
                this.showMainQuestionHierarchy(annotation);
                break;
            case 'question-text':
                // 题干文字框不显示层级内容
                hierarchySection.style.display = 'none';
                break;
            case 'sub-question':
                this.showSubQuestionHierarchy(annotation);
                break;
            case 'answer-area':
            case 'image-area':
                hierarchySection.style.display = 'none';
                break;
            default:
                hierarchySection.style.display = 'none';
        }
    }

    /**
     * 显示大题的层级内容（小题和配图）
     * @param {Object} mainQuestion 大题标注
     */
    showMainQuestionHierarchy(mainQuestion) {
        const hierarchySection = this.elements.panels.hierarchySection;
        const hierarchyTitle = this.elements.panels.hierarchyTitle;
        const hierarchyContent = this.elements.panels.hierarchyContent;

        hierarchySection.style.display = 'block';
        hierarchyTitle.textContent = `大题${mainQuestion.number} - 子内容`;

        // 获取该大题下的所有小题和配图
        const subQuestions = this.getChildAnnotations(mainQuestion.id, 'sub-question');
        const imageAreas = this.getChildAnnotations(mainQuestion.id, 'image-area');

        let html = '';

        // 显示小题列表
        if (subQuestions.length > 0) {
            html += '<div class="hierarchy-group">';
            html += '<h4 class="hierarchy-group-title">小题列表</h4>';
            subQuestions.forEach(subQuestion => {
                const coordinates = Utils.formatCoordinates(subQuestion.coordinates);
                html += `
                    <div class="annotation-item sub-question" data-id="${subQuestion.id}" onclick="uiManager.selectAnnotationById('${subQuestion.id}')">
                        <div class="annotation-type">小题${subQuestion.number}</div>
                        <div class="annotation-content">${subQuestion.attributes.content || '无内容'}</div>
                        <div class="annotation-coords">${coordinates}</div>
                    </div>
                `;
            });
            html += '</div>';
        }

        // 显示配图列表
        if (imageAreas.length > 0) {
            html += '<div class="hierarchy-group">';
            html += '<h4 class="hierarchy-group-title">配图列表</h4>';
            imageAreas.forEach(imageArea => {
                const coordinates = Utils.formatCoordinates(imageArea.coordinates);
                html += `
                    <div class="annotation-item image-area" data-id="${imageArea.id}" onclick="uiManager.selectAnnotationById('${imageArea.id}')">
                        <div class="annotation-type">配图${imageArea.number}</div>
                        <div class="annotation-content">${imageArea.attributes.imageDescription || '无描述'}</div>
                        <div class="annotation-coords">${coordinates}</div>
                    </div>
                `;
            });
            html += '</div>';
        }

        if (html === '') {
            html = '<p class="empty-list">该大题暂无小题和配图</p>';
        }

        hierarchyContent.innerHTML = html;
    }

    /**
     * 显示小题的层级内容（答题区域）
     * @param {Object} subQuestion 小题标注
     */
    showSubQuestionHierarchy(subQuestion) {
        const hierarchySection = this.elements.panels.hierarchySection;
        const hierarchyTitle = this.elements.panels.hierarchyTitle;
        const hierarchyContent = this.elements.panels.hierarchyContent;

        hierarchySection.style.display = 'block';
        hierarchyTitle.textContent = `小题${subQuestion.number} - 答题区域`;

        // 获取该小题下的所有答题区域
        const answerAreas = this.getChildAnnotations(subQuestion.id, 'answer-area');

        let html = '';

        if (answerAreas.length > 0) {
            html += '<div class="hierarchy-group">';
            html += '<h4 class="hierarchy-group-title">答题区域列表</h4>';
            answerAreas.forEach(answerArea => {
                const coordinates = Utils.formatCoordinates(answerArea.coordinates);
                const gradeResult = answerArea.attributes.gradeResult || '未批改';
                const gradeClass = gradeResult === '正确' ? 'correct' : gradeResult === '错误' ? 'incorrect' : 'partial';

                html += `
                    <div class="annotation-item answer-area" data-id="${answerArea.id}" onclick="uiManager.selectAnnotationById('${answerArea.id}')">
                        <div class="annotation-type">
                            答题区域${answerArea.number}
                            <span class="grade-badge ${gradeClass}">${gradeResult}</span>
                        </div>
                        <div class="annotation-content">${answerArea.attributes.answerContent || '无内容'}</div>
                        <div class="annotation-coords">${coordinates}</div>
                    </div>
                `;
            });
            html += '</div>';
        } else {
            html = '<p class="empty-list">该小题暂无答题区域</p>';
        }

        hierarchyContent.innerHTML = html;
    }

    /**
     * 获取指定父级下的子标注
     * @param {string} parentId 父级标注ID
     * @param {string} type 子标注类型
     * @returns {Array} 子标注数组
     */
    getChildAnnotations(parentId, type) {
        // 这个方法需要从主应用获取标注数据
        // 通过事件系统请求数据
        const event = new CustomEvent('getChildAnnotations', {
            detail: { parentId, type }
        });
        document.dispatchEvent(event);

        // 返回临时存储的结果
        return this.tempChildAnnotations || [];
    }

    /**
     * 根据ID选择标注
     * @param {string} annotationId 标注ID
     */
    selectAnnotationById(annotationId) {
        this.dispatchEvent('annotationSelect', { annotationId });
    }

    /**
     * 更新题目导航
     * @param {Array} annotations 标注数组
     */
    updateQuestionNavigation(annotations) {
        if (!this.elements.questionNavigation) return;

        const mainQuestions = annotations.filter(ann => ann.type === 'main-question')
            .sort((a, b) => a.number - b.number);

        if (mainQuestions.length === 0) {
            this.elements.questionNavigation.innerHTML = '<p class="empty-navigation">当前图片没有题目</p>';
            return;
        }

        let navigationHTML = '';

        mainQuestions.forEach(mainQuestion => {
            const subQuestions = annotations.filter(ann =>
                ann.type === 'sub-question' && ann.parentId === mainQuestion.id
            ).sort((a, b) => a.number - b.number);

            // 配图区域直接属于大题
            const imageAreas = annotations.filter(ann =>
                ann.type === 'image-area' && ann.parentId === mainQuestion.id
            ).sort((a, b) => a.number - b.number);

            const hasChildren = subQuestions.length > 0 || imageAreas.length > 0;
            const content = `大题${mainQuestion.number}`;

            navigationHTML += `
                <div class="nav-item" data-annotation-id="${mainQuestion.id}">
                    <div class="nav-item-header">
                        <div class="nav-item-main" onclick="uiManager.onNavigationItemClick('${mainQuestion.id}')">
                            ${hasChildren ? `<span class="nav-item-toggle" onclick="uiManager.toggleNavigationItem(event, '${mainQuestion.id}')">▶</span>` : '<span class="nav-item-toggle"></span>'}
                            <span class="nav-item-content" title="${content}">${content}</span>
                            <span class="nav-item-type">大题</span>
                        </div>
                        <button class="nav-ocr-btn" data-main-question-id="${mainQuestion.id}" onclick="uiManager.onMainQuestionOCR(event, '${mainQuestion.id}')" title="OCR识别此大题">
                            <span class="ocr-icon">🔍</span>
                            <span class="ocr-loading" style="display: none;">
                                <span class="spinner-wave"></span>
                            </span>
                        </button>
                    </div>
                    ${hasChildren ? `<div class="nav-children" id="nav-children-${mainQuestion.id}">` : ''}
            `;

            // 添加子题目及其答题区域
            subQuestions.forEach(subQuestion => {
                const subContent = `小题${subQuestion.number}`;
                navigationHTML += `
                    <div class="nav-child nav-sub-question" data-annotation-id="${subQuestion.id}" onclick="uiManager.onNavigationItemClick('${subQuestion.id}')">
                        <span class="nav-child-content" title="${subContent}">${subContent}</span>
                        <span class="nav-child-type">小题</span>
                    </div>
                `;

                // 添加该小题的答题区域
                const subAnswerAreas = annotations.filter(ann =>
                    ann.type === 'answer-area' && ann.parentId === subQuestion.id
                ).sort((a, b) => a.number - b.number);

                subAnswerAreas.forEach(answerArea => {
                    const answerContent = `答题区域${answerArea.number}`;
                    navigationHTML += `
                        <div class="nav-child nav-answer-area" data-annotation-id="${answerArea.id}" onclick="uiManager.onNavigationItemClick('${answerArea.id}')" style="padding-left: 48px;">
                            <span class="nav-child-content" title="${answerContent}">${answerContent}</span>
                            <span class="nav-child-type">答题</span>
                        </div>
                    `;
                });
            });

            // 添加配图区域（直接属于大题）
            imageAreas.forEach(imageArea => {
                const imageContent = `配图区域${imageArea.number}`;
                navigationHTML += `
                    <div class="nav-child nav-image-area" data-annotation-id="${imageArea.id}" onclick="uiManager.onNavigationItemClick('${imageArea.id}')">
                        <span class="nav-child-content" title="${imageContent}">${imageContent}</span>
                        <span class="nav-child-type">配图</span>
                    </div>
                `;
            });

            if (hasChildren) {
                navigationHTML += '</div>';
            }
            navigationHTML += '</div>';
        });

        this.elements.questionNavigation.innerHTML = navigationHTML;
    }

    /**
     * 导航项点击事件
     * @param {string} annotationId 标注ID
     */
    onNavigationItemClick(annotationId) {
        // 清除之前的选中状态
        const prevSelected = this.elements.questionNavigation.querySelectorAll('.selected');
        prevSelected.forEach(el => el.classList.remove('selected'));

        // 设置当前选中状态
        const currentItem = this.elements.questionNavigation.querySelector(`[data-annotation-id="${annotationId}"]`);
        if (currentItem) {
            const header = currentItem.classList.contains('nav-item') ?
                currentItem.querySelector('.nav-item-header') : currentItem;
            header.classList.add('selected');
        }

        // 触发标注选择事件
        this.dispatchEvent('annotationSelect', { annotationId });
    }

    /**
     * 切换导航项展开/折叠
     * @param {Event} event 事件对象
     * @param {string} annotationId 标注ID
     */
    toggleNavigationItem(event, annotationId) {
        event.stopPropagation(); // 阻止冒泡到父级点击事件

        const toggle = event.target;
        const children = document.getElementById(`nav-children-${annotationId}`);

        if (children) {
            const isExpanded = children.classList.contains('expanded');

            if (isExpanded) {
                children.classList.remove('expanded');
                toggle.classList.remove('expanded');
                toggle.textContent = '▶';
            } else {
                children.classList.add('expanded');
                toggle.classList.add('expanded');
                toggle.textContent = '▼';
            }
        }
    }

    /**
     * 更新导航选中状态
     * @param {string|null} annotationId 标注ID
     */
    updateNavigationSelection(annotationId) {
        if (!this.elements.questionNavigation) return;

        // 清除之前的选中状态
        const prevSelected = this.elements.questionNavigation.querySelectorAll('.selected');
        prevSelected.forEach(el => el.classList.remove('selected'));

        if (annotationId) {
            // 设置当前选中状态
            const currentItem = this.elements.questionNavigation.querySelector(`[data-annotation-id="${annotationId}"]`);
            if (currentItem) {
                const header = currentItem.classList.contains('nav-item') ?
                    currentItem.querySelector('.nav-item-header') : currentItem;
                if (header) {
                    header.classList.add('selected');

                    // 如果是子项，确保父项展开
                    if (currentItem.classList.contains('nav-child')) {
                        const parentItem = currentItem.closest('.nav-item');
                        if (parentItem) {
                            const parentId = parentItem.dataset.annotationId;
                            const children = document.getElementById(`nav-children-${parentId}`);
                            const toggle = parentItem.querySelector('.nav-item-toggle');

                            if (children && !children.classList.contains('expanded')) {
                                children.classList.add('expanded');
                                toggle.classList.add('expanded');
                                toggle.textContent = '▼';
                            }
                        }
                    }
                }
            }
        }
    }




    /**
     * 显示保存状态
     * @param {string} message 消息
     * @param {string} type 类型
     */
    showSaveStatus(message, type) {
        // 可以在状态栏或通知区域显示保存状态
        const statusElement = document.getElementById('saveStatus');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `save-status ${type}`;
        }
    }

    /**
     * 设置OCR事件监听器
     */
    setupOCREventListeners() {
        // OCR提供商选择事件
        if (this.elements.ocr.provider) {
            this.elements.ocr.provider.addEventListener('change', (e) => {
                this.onOCRProviderChange(e.target.value);
            });
        }

        // OCR模型选择事件
        if (this.elements.ocr.model) {
            this.elements.ocr.model.addEventListener('change', (e) => {
                this.onOCRModelChange(e.target.value);
            });
        }



        // 开始OCR按钮事件
        const startOCRButton = document.getElementById('startOCR');
        if (startOCRButton) {
            startOCRButton.addEventListener('click', () => {
                this.onCurrentAnnotationOCR();
            });
        }

        // 取消OCR按钮事件
        if (this.elements.ocr.cancelButton) {
            this.elements.ocr.cancelButton.addEventListener('click', () => {
                this.onCancelOCR();
            });
        }

        // 初始化OCR模型选项
        this.updateOCRModelOptions();
    }

    /**
     * OCR提供商改变事件
     * @param {string} provider 提供商
     */
    onOCRProviderChange(provider) {
        this.updateOCRModelOptions(provider);
    }

    /**
     * OCR模型改变事件
     * @param {string} model 模型
     */
    onOCRModelChange(model) {
        // 可以在这里添加模型特定的逻辑
        console.log('OCR模型已切换:', model);
    }



    /**
     * 显示设置弹窗
     */
    showSettings() {
        // 加载当前设置到弹窗
        this.loadCurrentSettingsToModal();

        // 显示弹窗
        const modal = this.elements.settings.modal;
        if (modal) {
            modal.classList.add('show');
            document.body.classList.add('modal-open');
        }
    }

    /**
     * 切换设置标签页
     */
    switchSettingsTab(tabName) {
        // 切换标签页激活状态
        this.elements.settings.tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // 切换面板显示状态
        this.elements.settings.panels.forEach(panel => {
            panel.classList.toggle('active', panel.id === tabName + 'Settings');
        });
    }

    /**
     * 加载当前设置到弹窗
     */
    loadCurrentSettingsToModal() {
        // 加载模型列表
        this.loadModelOptions();

        // 触发事件获取当前配置
        this.dispatchEvent('requestCurrentConfig', {});
    }

    /**
     * 动态加载模型选项
     */
    loadModelOptions() {
        // 触发事件获取可用模型列表
        this.dispatchEvent('requestAvailableModels', {});
    }

    /**
     * 更新模型选项
     * @param {Array} models 模型列表
     */
    updateModelOptions(models) {
        const modelSelect = this.elements.settings.model;
        if (!modelSelect) return;

        // 清空现有选项
        modelSelect.innerHTML = '';

        // 添加模型选项
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = `${model.name} (${model.provider})`;
            modelSelect.appendChild(option);
        });
    }

    /**
     * 开始OCR处理（已废弃，现在使用onMainQuestionOCR）
     */
    onStartOCR() {
        // 检查是否选中了大题
        if (!this.selectedAnnotation || this.selectedAnnotation.type !== 'main-question') {
            Utils.showNotification('请先选择一个大题区域', 'warning');
            return;
        }

        // 触发OCR开始事件
        this.dispatchEvent('ocrStart', {
            mainQuestionId: this.selectedAnnotation.id
        });
    }

    /**
     * 处理大题OCR点击事件
     * @param {Event} event 点击事件
     * @param {string} mainQuestionId 大题ID
     */
    onMainQuestionOCR(event, mainQuestionId) {
        // 阻止事件冒泡，避免触发导航项点击
        event.stopPropagation();

        // 显示该大题的加载状态
        this.showMainQuestionOCRLoading(mainQuestionId, true);

        // 触发OCR开始事件
        this.dispatchEvent('ocrStart', {
            mainQuestionId: mainQuestionId
        });
    }

    /**
     * 处理当前选中标注的OCR（用于OCR面板按钮）
     */
    onCurrentAnnotationOCR() {
        if (!this.selectedAnnotation) {
            Utils.showNotification('请先选择一个标注区域', 'warning');
            return;
        }

        let mainQuestionId;
        if (this.selectedAnnotation.type === 'main-question') {
            mainQuestionId = this.selectedAnnotation.id;
        } else if (this.selectedAnnotation.type === 'question-text') {
            mainQuestionId = this.selectedAnnotation.parentId;
        } else {
            Utils.showNotification('只能对大题或题干文字框进行OCR识别', 'warning');
            return;
        }

        // 显示该大题的加载状态
        this.showMainQuestionOCRLoading(mainQuestionId, true);

        // 触发OCR开始事件
        this.dispatchEvent('ocrStart', {
            mainQuestionId: mainQuestionId
        });
    }

    /**
     * 显示/隐藏特定大题的OCR加载状态
     * @param {string} mainQuestionId 大题ID
     * @param {boolean} show 是否显示加载状态
     */
    showMainQuestionOCRLoading(mainQuestionId, show) {
        const ocrBtn = document.querySelector(`[data-main-question-id="${mainQuestionId}"]`);
        if (!ocrBtn) return;

        if (show) {
            ocrBtn.classList.add('loading');
            ocrBtn.disabled = true;
        } else {
            ocrBtn.classList.remove('loading');
            ocrBtn.disabled = false;
        }
    }



    /**
     * 取消OCR处理
     */
    onCancelOCR() {
        // 隐藏所有大题的加载状态
        const ocrBtns = document.querySelectorAll('.nav-ocr-btn');
        ocrBtns.forEach(btn => {
            btn.classList.remove('loading');
            btn.disabled = false;
        });

        this.dispatchEvent('ocrCancel');
    }

    /**
     * 更新OCR模型选项
     * @param {string} provider 提供商
     */
    updateOCRModelOptions(provider = null) {
        if (!this.elements.ocr.model) return;

        const currentProvider = provider || this.elements.ocr.provider.value;
        const modelSelect = this.elements.ocr.model;

        // 清空现有选项
        modelSelect.innerHTML = '<option value="">默认模型</option>';

        // 根据提供商添加模型选项
        const models = {
            doubao: {
                'doubao-seed-1-6-flash-250715': '豆包 Flash (推荐)',
                'doubao-seed-1-6-250615': '豆包 标准版',
                'doubao-1-5-thinking-vision-pro-250428': '豆包 思考版'
            },
            qwen: {
                'qwen-vl-plus-latest': '通义千问 Plus (推荐)',
                'qwen-vl-ocr-latest': '通义千问 OCR专版'
            }
        };

        if (models[currentProvider]) {
            Object.entries(models[currentProvider]).forEach(([value, text]) => {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = text;
                modelSelect.appendChild(option);
            });
        }
    }

    /**
     * 显示OCR面板
     * @param {Object} annotation 选中的标注
     */
    showOCRPanel(annotation) {
        if (!this.elements.ocr.section) return;

        // 大题和题干文字框都显示OCR面板
        if (annotation && (annotation.type === 'main-question' || annotation.type === 'question-text')) {
            this.elements.ocr.section.style.display = 'block';
        } else {
            this.elements.ocr.section.style.display = 'none';
        }
    }

    /**
     * 更新OCR进度
     * @param {Object} progress 进度信息
     */
    updateOCRProgress(progress) {
        if (!this.elements.ocr.progress) return;

        this.elements.ocr.progress.style.display = 'block';

        if (this.elements.ocr.progressText) {
            this.elements.ocr.progressText.textContent = `正在处理 ${progress.current}/${progress.total}`;
        }

        if (this.elements.ocr.progressPercent) {
            this.elements.ocr.progressPercent.textContent = `${progress.percentage}%`;
        }

        if (this.elements.ocr.progressFill) {
            this.elements.ocr.progressFill.style.width = `${progress.percentage}%`;
        }

        // 禁用开始按钮
        if (this.elements.ocr.startButton) {
            this.elements.ocr.startButton.disabled = true;
        }
    }

    /**
     * 隐藏OCR进度
     */
    hideOCRProgress() {
        if (this.elements.ocr.progress) {
            this.elements.ocr.progress.style.display = 'none';
        }

        // 启用开始按钮
        if (this.elements.ocr.startButton) {
            this.elements.ocr.startButton.disabled = false;
        }
    }

    /**
     * 显示OCR结果
     * @param {Array} results OCR结果列表
     */
    showOCRResults(results) {
        if (!this.elements.ocr.results || !this.elements.ocr.resultsList) return;

        this.elements.ocr.results.style.display = 'block';

        let html = '';
        results.forEach(result => {
            const statusClass = result.success ? 'success' : 'error';
            const typeNames = {
                'main-question': '大题',
                'question-text': '题干文字',
                'sub-question': '小题',
                'answer-area': '答题区域',
                'image-area': '配图区域'
            };

            // 通过ID获取标注对象，然后获取编号
            const annotation = window.ocrTool.annotationManager.getAnnotationById(result.annotationId);
            const displayName = annotation ?
                ((annotation.type === 'question-text' || annotation.number === null) ?
                    typeNames[result.type] : `${typeNames[result.type]}${annotation.number}`) :
                `${typeNames[result.type] || result.type}`;

            html += `
                <div class="result-item ${statusClass}">
                    <div class="result-header">
                        ${displayName}
                    </div>
                    <div class="result-content">
                        ${result.success ? result.ocrText : result.error}
                    </div>
                    ${result.success ? `
                        <div class="result-meta">
                            <span>提供商: ${result.provider}</span>
                            <span>Token: ${result.tokens}</span>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        this.elements.ocr.resultsList.innerHTML = html;
    }

    /**
     * 隐藏OCR结果
     */
    hideOCRResults() {
        if (this.elements.ocr.results) {
            this.elements.ocr.results.style.display = 'none';
        }
    }

    /**
     * 销毁UI管理器
     */
    destroy() {
        this.elements = {};
        this.selectedAnnotation = null;
    }
}

// 创建全局UI管理器实例
window.uiManager = new UIManager();
