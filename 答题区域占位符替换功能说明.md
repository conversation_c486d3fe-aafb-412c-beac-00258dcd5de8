# 答题区域占位符替换功能说明

## 功能概述

在OCR识别题干内容时，系统现在支持自动将题干中的"{答题区域X}"占位符替换为对应答题区域的实际OCR识别结果，并保持原有的括号格式。

**重要说明**：答题区域编号在整个大题范围内是唯一且递增的，不是每个小题内从1开始编号。例如，一道大题中可能有答题区域1、2、3、4等，这些编号在整个大题中是连续且唯一的。

## 功能特点

### 1. 两步处理策略

系统采用两步处理策略确保正确的占位符替换：

**第一步：处理答题区域**
- 先处理所有答题区域的OCR结果
- 确保答题区域内容已更新
- 建立答题区域编号到内容的映射关系（基于大题范围内的唯一编号）

**第二步：处理题干文字**
- 处理题干文字等其他标注
- 使用已更新的答题区域内容替换占位符
- 保持原有的括号格式

### 2. 大题范围内的编号系统

- 答题区域编号在整个大题范围内是唯一且递增的
- 不同小题的答题区域编号不会重复
- 编号按创建顺序递增：1、2、3、4...

### 3. 灵活的占位符格式支持

系统支持多种占位符格式：
- `{答题区域1}` - 标准格式
- `{答题区域 1}` - 带空格格式
- `{答题区域1}` - 紧凑格式

### 4. 智能回退机制

如果精确匹配失败，系统会：
- 尝试按顺序替换未匹配的占位符
- 使用已有的答题区域内容（即使没有进行OCR）
- 保持无法匹配的占位符原样

## 使用示例

### 大题结构示例

```
大题1
├── 小题1
│   ├── 答题区域1：赵
│   └── 答题区域2：设计史
├── 小题2
│   ├── 答题区域3：智慧
│   └── 答题区域4：创
└── 题干文字框：zhào（{答题区域1}）县的安济桥是我国shè jì shǐ（{答题区域2}）上充满zhì huì（{答题区域3}）的chuàng（{答题区域4}）作。
```

### 输入示例

**题干文字（OCR识别前）：**
```
zhào（{答题区域1}）县的安济桥是我国shè jì shǐ（{答题区域2}）上充满zhì huì（{答题区域3}）的chuàng（{答题区域4}）作。
```

**答题区域内容（大题范围内编号）：**
- 答题区域1：赵
- 答题区域2：设计史
- 答题区域3：智慧
- 答题区域4：创

### 输出结果

**题干文字（OCR识别后）：**
```
zhào（赵）县的安济桥是我国shè jì shǐ（设计史）上充满zhì huì（智慧）的chuàng（创）作。
```

## 技术实现

### 核心逻辑

1. **获取大题范围内的答题区域**
   ```javascript
   const allAnswerAreas = allAnnotations.filter(ann => 
       ann.type === 'answer-area' && 
       ann.parentId && 
       allAnnotations.some(subQ => 
           subQ.id === ann.parentId && 
           subQ.parentId === mainQuestionAnnotation.id
       )
   ).sort((a, b) => a.number - b.number);
   ```

2. **答题区域映射建立**
   ```javascript
   const answerAreaMap = new Map();
   // 存储答题区域编号到内容的映射（基于大题范围内的唯一编号）
   answerAreaMap.set(1, "赵");
   answerAreaMap.set(2, "设计史");
   answerAreaMap.set(3, "智慧");
   answerAreaMap.set(4, "创");
   ```

3. **占位符替换**
   ```javascript
   // 支持多种格式的正则表达式
   processedText = processedText.replace(/\{答题区域\s*(\d+)\s*\}/g, (match, number) => {
       const answerContent = answerAreaMap.get(parseInt(number));
       return answerContent || match;
   });
   ```

## 调试信息

系统提供详细的调试日志，包括：
- 大题范围内答题区域的收集和排序
- 答题区域映射建立过程
- 占位符匹配和替换过程
- 替换前后的文字对比

在浏览器控制台中查看以 `🔍 [OCR调试]` 开头的日志信息。

## 注意事项

1. **编号系统**：答题区域编号在整个大题范围内唯一且递增
2. **占位符格式**：建议使用标准格式 `{答题区域X}`
3. **处理顺序**：系统会先处理答题区域，再处理题干文字
4. **内容保护**：无法匹配的占位符会保持原样，不会丢失
5. **跨小题引用**：题干文字可以引用不同小题中的答题区域

## 兼容性

- 兼容现有的OCR处理流程
- 不影响其他类型标注的处理
- 向后兼容，不会破坏现有功能
- 支持大题范围内的答题区域编号系统
