# 画布尺寸同步修复

## 问题描述
当视图缩小时，画布的大小和中间图片区域的宽度不一致，导致画布在某个边界消失，视觉上出现不协调的情况。

## 根本原因
原来的`updateCanvas()`方法将画布尺寸设置为整个容器的尺寸，但图片由于`object-fit: contain`样式，可能只占容器的一部分区域。这导致：

1. **画布覆盖整个容器**：画布铺满整个image-container
2. **图片只占部分区域**：图片根据比例居中显示，可能小于容器
3. **视觉不一致**：标注框可能出现在图片外的空白区域

## 修复方案

### 修改前的代码
```javascript
updateCanvas() {
    if (!this.canvasElement) return;
    
    const container = this.canvasElement.parentElement;
    const containerRect = container.getBoundingClientRect();
    
    // 设置画布尺寸为容器尺寸 - 问题所在
    this.canvasElement.width = containerRect.width;
    this.canvasElement.height = containerRect.height;
    this.canvasElement.style.width = containerRect.width + 'px';
    this.canvasElement.style.height = containerRect.height + 'px';
}
```

### 修复后的代码
```javascript
updateCanvas() {
    if (!this.canvasElement) return;
    
    const container = this.canvasElement.parentElement;
    const containerRect = container.getBoundingClientRect();
    
    // 如果有图片，让画布与图片区域保持一致
    if (this.imageElement && this.imageElement.complete) {
        const imageRect = this.imageElement.getBoundingClientRect();
        
        // 计算图片相对于容器的位置
        const imageLeft = imageRect.left - containerRect.left;
        const imageTop = imageRect.top - containerRect.top;
        
        // 设置画布尺寸为图片实际显示尺寸
        this.canvasElement.width = imageRect.width;
        this.canvasElement.height = imageRect.height;
        this.canvasElement.style.width = imageRect.width + 'px';
        this.canvasElement.style.height = imageRect.height + 'px';
        
        // 设置画布位置与图片对齐
        this.canvasElement.style.left = imageLeft + 'px';
        this.canvasElement.style.top = imageTop + 'px';
    } else {
        // 兼容性处理：没有图片时使用容器尺寸
        this.canvasElement.width = containerRect.width;
        this.canvasElement.height = containerRect.height;
        this.canvasElement.style.width = containerRect.width + 'px';
        this.canvasElement.style.height = containerRect.height + 'px';
        this.canvasElement.style.left = '0px';
        this.canvasElement.style.top = '0px';
    }
}
```

## 修复效果

### 1. 画布与图片完全对齐
- 画布尺寸 = 图片实际显示尺寸
- 画布位置 = 图片在容器中的位置
- 标注框只出现在图片区域内

### 2. 响应式适配
- 窗口缩放时自动调整
- 图片缩放时同步更新
- 图片平移时保持对齐

### 3. 兼容性保证
- 图片未加载时使用容器尺寸
- 保持现有功能不受影响

## 触发更新的场景

修复后，以下操作都会正确更新画布位置：

1. **窗口大小改变** → `resize事件` → `updateDisplay()` → `updateImageInfo()` → `updateCanvas()`
2. **图片缩放** → `setZoom()` → `updateImageInfo()` → `updateCanvas()`
3. **图片平移** → `setPan()` → `updateImageInfo()` → `updateCanvas()`
4. **图片加载** → `onImageLoaded()` → `updateDisplay()` → `updateImageInfo()` → `updateCanvas()`

## 测试建议

1. **加载不同比例的图片**（横图、竖图、正方形）
2. **缩小浏览器窗口**，观察画布是否与图片保持一致
3. **缩放图片**，检查标注框是否正确显示
4. **平移图片**，验证画布是否跟随移动
5. **在不同屏幕分辨率下测试**

这个修复确保了画布始终与图片区域完美对齐，解决了视觉不一致的问题。
