/**
 * OCR标注工具 - 坐标系统
 * 实现精确的坐标转换和计算
 */

class CoordinateSystem {
    constructor(imageElement, canvasElement) {
        this.imageElement = imageElement;
        this.canvasElement = canvasElement;
        this.zoomLevel = 1;
        this.panX = 0;
        this.panY = 0;
        this.imageOffset = { x: 0, y: 0 };
        this.imageSize = { width: 0, height: 0 };
        this.naturalSize = { width: 0, height: 0 };
        
        this.updateImageInfo();
    }

    /**
     * 更新图片信息
     */
    updateImageInfo() {
        if (!this.imageElement || !this.imageElement.complete) return;
        
        const rect = this.imageElement.getBoundingClientRect();
        const containerRect = this.imageElement.parentElement.getBoundingClientRect();
        
        // 图片在容器中的实际显示尺寸
        this.imageSize = {
            width: rect.width,
            height: rect.height
        };
        
        // 图片在容器中的偏移位置（包含平移）
        this.imageOffset = {
            x: rect.left - containerRect.left,
            y: rect.top - containerRect.top
        };
        
        // 图片的原始尺寸
        this.naturalSize = {
            width: this.imageElement.naturalWidth,
            height: this.imageElement.naturalHeight
        };
        
        // 更新画布尺寸和位置
        this.updateCanvas();
    }

    /**
     * 更新画布尺寸和位置
     */
    updateCanvas() {
        if (!this.canvasElement) return;
        
        const container = this.canvasElement.parentElement;
        const containerRect = container.getBoundingClientRect();
        
        // 设置画布尺寸为容器尺寸
        this.canvasElement.width = containerRect.width;
        this.canvasElement.height = containerRect.height;
        this.canvasElement.style.width = containerRect.width + 'px';
        this.canvasElement.style.height = containerRect.height + 'px';
    }

    /**
     * 容器坐标转换为图片坐标
     * @param {number} containerX 容器X坐标
     * @param {number} containerY 容器Y坐标
     * @returns {Object} 图片坐标 {x, y}
     */
    containerToImage(containerX, containerY) {
        // 转换为相对于图片的坐标
        const relativeX = (containerX - this.imageOffset.x) / this.imageSize.width;
        const relativeY = (containerY - this.imageOffset.y) / this.imageSize.height;
        
        // 转换为图片原始坐标
        return {
            x: relativeX * this.naturalSize.width,
            y: relativeY * this.naturalSize.height
        };
    }

    /**
     * 图片坐标转换为容器坐标
     * @param {number} imageX 图片X坐标
     * @param {number} imageY 图片Y坐标
     * @returns {Object} 容器坐标 {x, y}
     */
    imageToContainer(imageX, imageY) {
        // 转换为相对坐标
        const relativeX = imageX / this.naturalSize.width;
        const relativeY = imageY / this.naturalSize.height;
        
        // 转换为容器坐标
        return {
            x: relativeX * this.imageSize.width + this.imageOffset.x,
            y: relativeY * this.imageSize.height + this.imageOffset.y
        };
    }

    /**
     * 鼠标事件坐标转换为图片坐标
     * @param {MouseEvent} event 鼠标事件
     * @returns {Object} 图片坐标 {x, y}
     */
    eventToImage(event) {
        const canvasRect = this.canvasElement.getBoundingClientRect();
        const containerX = event.clientX - canvasRect.left;
        const containerY = event.clientY - canvasRect.top;
        
        return this.containerToImage(containerX, containerY);
    }

    /**
     * 图片坐标转换为画布坐标
     * @param {number} imageX 图片X坐标
     * @param {number} imageY 图片Y坐标
     * @returns {Object} 画布坐标 {x, y}
     */
    imageToCanvas(imageX, imageY) {
        const containerCoords = this.imageToContainer(imageX, imageY);
        return {
            x: containerCoords.x,
            y: containerCoords.y
        };
    }

    /**
     * 坐标数组转换（图片坐标到画布坐标）
     * @param {Array} coordinates 坐标数组 [[x1, y1], [x2, y2]]
     * @returns {Object} 矩形坐标 {x, y, width, height}
     */
    coordinatesToRect(coordinates) {
        if (!Utils.isValidCoordinates(coordinates)) {
            return { x: 0, y: 0, width: 0, height: 0 };
        }
        
        const [start, end] = coordinates;
        const startCanvas = this.imageToCanvas(start[0], start[1]);
        const endCanvas = this.imageToCanvas(end[0], end[1]);
        
        return {
            x: Math.min(startCanvas.x, endCanvas.x),
            y: Math.min(startCanvas.y, endCanvas.y),
            width: Math.abs(endCanvas.x - startCanvas.x),
            height: Math.abs(endCanvas.y - startCanvas.y)
        };
    }

    /**
     * 矩形坐标转换为坐标数组
     * @param {Object} rect 矩形 {x, y, width, height}
     * @returns {Array} 坐标数组 [[x1, y1], [x2, y2]]
     */
    rectToCoordinates(rect) {
        const startImage = this.containerToImage(rect.x, rect.y);
        const endImage = this.containerToImage(rect.x + rect.width, rect.y + rect.height);
        
        return [
            [Math.round(startImage.x), Math.round(startImage.y)],
            [Math.round(endImage.x), Math.round(endImage.y)]
        ];
    }

    /**
     * 检查坐标是否在图片范围内
     * @param {number} imageX 图片X坐标
     * @param {number} imageY 图片Y坐标
     * @returns {boolean} 是否在范围内
     */
    isInImageBounds(imageX, imageY) {
        return imageX >= 0 && 
               imageX <= this.naturalSize.width && 
               imageY >= 0 && 
               imageY <= this.naturalSize.height;
    }

    /**
     * 检查容器坐标是否在图片区域内
     * @param {number} containerX 容器X坐标
     * @param {number} containerY 容器Y坐标
     * @returns {boolean} 是否在图片区域内
     */
    isInImageArea(containerX, containerY) {
        return containerX >= this.imageOffset.x && 
               containerX <= this.imageOffset.x + this.imageSize.width && 
               containerY >= this.imageOffset.y && 
               containerY <= this.imageOffset.y + this.imageSize.height;
    }

    /**
     * 限制坐标在图片范围内
     * @param {number} imageX 图片X坐标
     * @param {number} imageY 图片Y坐标
     * @returns {Object} 限制后的坐标 {x, y}
     */
    clampToImageBounds(imageX, imageY) {
        return {
            x: Utils.clamp(imageX, 0, this.naturalSize.width),
            y: Utils.clamp(imageY, 0, this.naturalSize.height)
        };
    }

    /**
     * 计算缩放后的坐标
     * @param {number} x X坐标
     * @param {number} y Y坐标
     * @param {number} zoom 缩放级别
     * @returns {Object} 缩放后的坐标 {x, y}
     */
    applyZoom(x, y, zoom) {
        return {
            x: x * zoom,
            y: y * zoom
        };
    }

    /**
     * 设置缩放级别
     * @param {number} zoom 缩放级别
     */
    setZoom(zoom) {
        this.zoomLevel = zoom;
        this.updateImageInfo();
    }

    /**
     * 设置平移偏移
     * @param {number} panX X轴平移
     * @param {number} panY Y轴平移
     */
    setPan(panX, panY) {
        this.panX = panX;
        this.panY = panY;
        this.updateImageInfo();
    }

    /**
     * 获取当前缩放级别
     * @returns {number} 缩放级别
     */
    getZoom() {
        return this.zoomLevel;
    }

    /**
     * 获取图片信息
     * @returns {Object} 图片信息
     */
    getImageInfo() {
        return {
            naturalSize: { ...this.naturalSize },
            displaySize: { ...this.imageSize },
            offset: { ...this.imageOffset },
            zoom: this.zoomLevel
        };
    }

    /**
     * 计算适合容器的缩放级别
     * @param {number} containerWidth 容器宽度
     * @param {number} containerHeight 容器高度
     * @returns {number} 适合的缩放级别
     */
    calculateFitZoom(containerWidth, containerHeight) {
        if (!this.naturalSize.width || !this.naturalSize.height) return 1;
        
        const scaleX = containerWidth / this.naturalSize.width;
        const scaleY = containerHeight / this.naturalSize.height;
        
        return Math.min(scaleX, scaleY, 1); // 不超过100%
    }

    /**
     * 重置坐标系统
     */
    reset() {
        this.zoomLevel = 1;
        this.imageOffset = { x: 0, y: 0 };
        this.imageSize = { width: 0, height: 0 };
        this.naturalSize = { width: 0, height: 0 };
    }
}

// 导出坐标系统类
window.CoordinateSystem = CoordinateSystem;
