import type { Point, Rect } from '@/types'

/**
 * 计算两点之间的距离
 */
export function distance(p1: Point, p2: Point): number {
  const dx = p2.x - p1.x
  const dy = p2.y - p1.y
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 计算点到线段的距离
 */
export function pointToLineDistance(point: Point, lineStart: Point, lineEnd: Point): number {
  const A = point.x - lineStart.x
  const B = point.y - lineStart.y
  const C = lineEnd.x - lineStart.x
  const D = lineEnd.y - lineStart.y

  const dot = A * C + B * D
  const lenSq = C * C + D * D
  
  if (lenSq === 0) {
    return distance(point, lineStart)
  }
  
  let param = dot / lenSq

  let xx: number, yy: number

  if (param < 0) {
    xx = lineStart.x
    yy = lineStart.y
  } else if (param > 1) {
    xx = lineEnd.x
    yy = lineEnd.y
  } else {
    xx = lineStart.x + param * C
    yy = lineStart.y + param * D
  }

  const dx = point.x - xx
  const dy = point.y - yy
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 检查点是否在矩形内
 */
export function isPointInRect(point: Point, rect: Rect): boolean {
  return point.x >= rect.x &&
         point.x <= rect.x + rect.width &&
         point.y >= rect.y &&
         point.y <= rect.y + rect.height
}

/**
 * 检查点是否在多边形内（射线法）
 */
export function isPointInPolygon(point: Point, polygon: Point[]): boolean {
  if (polygon.length < 3) return false
  
  let inside = false
  const x = point.x
  const y = point.y
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].x
    const yi = polygon[i].y
    const xj = polygon[j].x
    const yj = polygon[j].y
    
    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside
    }
  }
  
  return inside
}

/**
 * 检查点是否在圆形内
 */
export function isPointInCircle(point: Point, center: Point, radius: number): boolean {
  return distance(point, center) <= radius
}

/**
 * 计算多边形的边界矩形
 */
export function getPolygonBounds(polygon: Point[]): Rect {
  if (polygon.length === 0) {
    return { x: 0, y: 0, width: 0, height: 0 }
  }
  
  let minX = polygon[0].x
  let maxX = polygon[0].x
  let minY = polygon[0].y
  let maxY = polygon[0].y
  
  for (let i = 1; i < polygon.length; i++) {
    minX = Math.min(minX, polygon[i].x)
    maxX = Math.max(maxX, polygon[i].x)
    minY = Math.min(minY, polygon[i].y)
    maxY = Math.max(maxY, polygon[i].y)
  }
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  }
}

/**
 * 计算多边形的中心点
 */
export function getPolygonCenter(polygon: Point[]): Point {
  if (polygon.length === 0) {
    return { x: 0, y: 0 }
  }
  
  let totalX = 0
  let totalY = 0
  
  for (const point of polygon) {
    totalX += point.x
    totalY += point.y
  }
  
  return {
    x: totalX / polygon.length,
    y: totalY / polygon.length
  }
}

/**
 * 计算多边形的面积
 */
export function getPolygonArea(polygon: Point[]): number {
  if (polygon.length < 3) return 0
  
  let area = 0
  for (let i = 0; i < polygon.length; i++) {
    const j = (i + 1) % polygon.length
    area += polygon[i].x * polygon[j].y
    area -= polygon[j].x * polygon[i].y
  }
  
  return Math.abs(area) / 2
}

/**
 * 标准化矩形（确保宽高为正数）
 */
export function normalizeRect(rect: Rect): Rect {
  const x = rect.width < 0 ? rect.x + rect.width : rect.x
  const y = rect.height < 0 ? rect.y + rect.height : rect.y
  const width = Math.abs(rect.width)
  const height = Math.abs(rect.height)
  
  return { x, y, width, height }
}

/**
 * 从两个点创建矩形
 */
export function rectFromPoints(p1: Point, p2: Point): Rect {
  const x = Math.min(p1.x, p2.x)
  const y = Math.min(p1.y, p2.y)
  const width = Math.abs(p2.x - p1.x)
  const height = Math.abs(p2.y - p1.y)
  
  return { x, y, width, height }
}

/**
 * 检查两个矩形是否相交
 */
export function rectsIntersect(rect1: Rect, rect2: Rect): boolean {
  return !(rect1.x + rect1.width < rect2.x ||
           rect2.x + rect2.width < rect1.x ||
           rect1.y + rect1.height < rect2.y ||
           rect2.y + rect2.height < rect1.y)
}

/**
 * 计算两个矩形的交集
 */
export function rectIntersection(rect1: Rect, rect2: Rect): Rect | null {
  if (!rectsIntersect(rect1, rect2)) {
    return null
  }
  
  const x = Math.max(rect1.x, rect2.x)
  const y = Math.max(rect1.y, rect2.y)
  const width = Math.min(rect1.x + rect1.width, rect2.x + rect2.width) - x
  const height = Math.min(rect1.y + rect1.height, rect2.y + rect2.height) - y
  
  return { x, y, width, height }
}

/**
 * 计算两个矩形的并集
 */
export function rectUnion(rect1: Rect, rect2: Rect): Rect {
  const x = Math.min(rect1.x, rect2.x)
  const y = Math.min(rect1.y, rect2.y)
  const width = Math.max(rect1.x + rect1.width, rect2.x + rect2.width) - x
  const height = Math.max(rect1.y + rect1.height, rect2.y + rect2.height) - y
  
  return { x, y, width, height }
}

/**
 * 缩放点坐标
 */
export function scalePoint(point: Point, scale: number, origin: Point = { x: 0, y: 0 }): Point {
  return {
    x: origin.x + (point.x - origin.x) * scale,
    y: origin.y + (point.y - origin.y) * scale
  }
}

/**
 * 平移点坐标
 */
export function translatePoint(point: Point, offset: Point): Point {
  return {
    x: point.x + offset.x,
    y: point.y + offset.y
  }
}

/**
 * 旋转点坐标
 */
export function rotatePoint(point: Point, angle: number, origin: Point = { x: 0, y: 0 }): Point {
  const cos = Math.cos(angle)
  const sin = Math.sin(angle)
  const dx = point.x - origin.x
  const dy = point.y - origin.y
  
  return {
    x: origin.x + dx * cos - dy * sin,
    y: origin.y + dx * sin + dy * cos
  }
}

/**
 * 计算点的变换矩阵
 */
export function transformPoint(
  point: Point,
  scale: number,
  offset: Point,
  rotation = 0,
  origin: Point = { x: 0, y: 0 }
): Point {
  let result = point
  
  // 先旋转
  if (rotation !== 0) {
    result = rotatePoint(result, rotation, origin)
  }
  
  // 再缩放
  if (scale !== 1) {
    result = scalePoint(result, scale, origin)
  }
  
  // 最后平移
  if (offset.x !== 0 || offset.y !== 0) {
    result = translatePoint(result, offset)
  }
  
  return result
}

/**
 * 反向变换点坐标
 */
export function inverseTransformPoint(
  point: Point,
  scale: number,
  offset: Point,
  rotation = 0,
  origin: Point = { x: 0, y: 0 }
): Point {
  let result = point
  
  // 反向平移
  if (offset.x !== 0 || offset.y !== 0) {
    result = translatePoint(result, { x: -offset.x, y: -offset.y })
  }
  
  // 反向缩放
  if (scale !== 1) {
    result = scalePoint(result, 1 / scale, origin)
  }
  
  // 反向旋转
  if (rotation !== 0) {
    result = rotatePoint(result, -rotation, origin)
  }
  
  return result
}

/**
 * 简化多边形（Douglas-Peucker算法）
 */
export function simplifyPolygon(polygon: Point[], tolerance = 1): Point[] {
  if (polygon.length <= 2) return polygon
  
  function douglasPeucker(points: Point[], epsilon: number): Point[] {
    if (points.length <= 2) return points
    
    // 找到距离起点和终点连线最远的点
    let maxDistance = 0
    let maxIndex = 0
    const start = points[0]
    const end = points[points.length - 1]
    
    for (let i = 1; i < points.length - 1; i++) {
      const dist = pointToLineDistance(points[i], start, end)
      if (dist > maxDistance) {
        maxDistance = dist
        maxIndex = i
      }
    }
    
    // 如果最大距离大于阈值，递归简化
    if (maxDistance > epsilon) {
      const left = douglasPeucker(points.slice(0, maxIndex + 1), epsilon)
      const right = douglasPeucker(points.slice(maxIndex), epsilon)
      
      return left.slice(0, -1).concat(right)
    } else {
      return [start, end]
    }
  }
  
  return douglasPeucker(polygon, tolerance)
}

/**
 * 检查多边形是否为顺时针方向
 */
export function isClockwise(polygon: Point[]): boolean {
  if (polygon.length < 3) return false
  
  let sum = 0
  for (let i = 0; i < polygon.length; i++) {
    const current = polygon[i]
    const next = polygon[(i + 1) % polygon.length]
    sum += (next.x - current.x) * (next.y + current.y)
  }
  
  return sum > 0
}

/**
 * 反转多边形顶点顺序
 */
export function reversePolygon(polygon: Point[]): Point[] {
  return [...polygon].reverse()
}
