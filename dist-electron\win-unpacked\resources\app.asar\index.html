<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR标注工具</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- 顶部工具栏 -->
    <header class="toolbar">
        <div class="toolbar-left">
            <!-- 安徽鸿途标识 -->
            <div class="company-logo">
                <span class="logo-text">安徽鸿途文化科技有限公司</span>
            </div>
            <div class="toolbar-divider"></div>
            <span class="image-counter">
                <span id="currentImageIndex">0</span> / <span id="totalImages">0</span>
            </span>
            <span class="question-counter" id="questionCounter" title="当前图片的题目数量">
                📝 <span id="currentQuestionCount">0</span> 道题
            </span>
        </div>
        <!-- 工作区管理按钮 -->
        <div class="workspace-controls">
            <button id="openWorkspace" class="btn btn-primary" title="打开工作区文件夹">
                <i class="icon-folder"></i>
                <span>打开工作区</span>
            </button>
            <button id="clearAll" class="btn btn-warning" title="清空当前图片的所有标注框" disabled>
                <i class="icon-clear-all"></i>
                <span>清空当前</span>
            </button>
        </div>
        
        <div class="toolbar-center">
            <button id="prevImage" class="btn btn-nav" title="上一张 (←)">
                <i class="icon-prev"></i>
            </button>
            <div class="image-info">
                <span id="currentImageName">未选择图片</span>
                <span class="separator">|</span>
                <span id="zoomLevel">100%</span>
            </div>
            <button id="nextImage" class="btn btn-nav" title="下一张 (→)">
                <i class="icon-next"></i>
            </button>
        </div>
        
        <div class="toolbar-right">

            <div class="zoom-controls">
                <button id="zoomOut" class="btn btn-zoom" title="缩小 (-)">
                    <i class="icon-zoom-out"></i>
                </button>
                <button id="zoomReset" class="btn btn-zoom" title="重置缩放和位置 (0)">
                    <i class="icon-zoom-reset"></i>
                </button>
                <button id="zoomIn" class="btn btn-zoom" title="放大 (+)">
                    <i class="icon-zoom-in"></i>
                </button>
            </div>
            <button id="toggleAnnotations" class="btn btn-toggle active" title="显示/隐藏标注 (Q)">
                <i class="icon-visibility"></i>
                <span>显示标注(Q)</span>
            </button>
            <select id="modeSelector" class="mode-selector" title="切换标注模式和质检模式 (W)">
                <option value="annotation">标注模式(W)</option>
                <option value="quality-check">质检模式(W)</option>
            </select>
            <button id="showShortcuts" class="btn btn-help" title="快捷键说明 (F1)">
                <i class="icon-help"></i>快捷键
            </button>
            <button id="showSettings" class="btn btn-settings" title="OCR设置">
                <i class="icon-settings"></i>设置
            </button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 左侧工具面板 -->
        <aside class="left-panel">
            <div class="panel-section">
                <h3>标注工具</h3>
                <div class="tool-buttons">
                    <button id="selectMainQuestion" class="tool-btn" data-tool="main-question" title="选择大题 (1)">
                        <i class="icon-main-question"></i>
                        <span>大题(1)</span>
                    </button>
                    <button id="selectQuestionText" class="tool-btn" data-tool="question-text" title="选择题干文字 (2)">
                        <i class="icon-question-text"></i>
                        <span>题干文字(2)</span>
                    </button>
                    <button id="selectSubQuestion" class="tool-btn" data-tool="sub-question" title="选择小题 (3)">
                        <i class="icon-sub-question"></i>
                        <span>小题(3)</span>
                    </button>
                    <button id="selectAnswerArea" class="tool-btn" data-tool="answer-area" title="选择答题区域 (4)">
                        <i class="icon-answer-area"></i>
                        <span>答题区域(4)</span>
                    </button>
                    <button id="selectImageArea" class="tool-btn" data-tool="image-area" title="选择配图区域 (5)">
                        <i class="icon-image-area"></i>
                        <span>配图区域(5)</span>
                    </button>
                    <button id="clearSelection" class="tool-btn" title="清除选择">
                        <i class="icon-clear"></i>
                        <span>清除选择(Esc)</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3>题目导航</h3>
                <div id="questionNavigation" class="question-navigation">
                    <p class="empty-navigation">当前图片没有题目</p>
                </div>
            </div>

        </aside>

        <!-- 中央图片显示区域 -->
        <section class="image-container">
            <div class="image-wrapper" id="imageWrapper">
                <img id="currentImage" alt="当前图片">
                <canvas id="annotationCanvas"></canvas>
            </div>
        </section>

        <!-- 右侧信息面板 -->
        <aside class="right-panel">
            <div class="panel-section">
                <h3>选中区域信息</h3>
                <div id="selectedAnnotationInfo" class="annotation-info">
                    <p class="no-selection">请选择一个标注区域</p>
                </div>
            </div>

            <div class="panel-section" id="hierarchySection" style="display: none;">
                <h3 id="hierarchyTitle">层级内容</h3>
                <div id="hierarchyContent" class="hierarchy-content">
                    <p class="empty-list">请选择一个标注区域</p>
                </div>
            </div>

            <!-- OCR进度和结果显示 -->
            <div class="panel-section" id="ocrSection" style="display: none;">
                <h3>OCR识别</h3>
                <div id="ocrResults" class="ocr-results" style="display: none;">
                    <h4>OCR结果</h4>
                    <div id="ocrResultsList" class="results-list"></div>
                </div>
            </div>

            <div class="panel-section quality-check-section" style="display: none;">
                <h3>质检信息</h3>
                <div id="qualityCheckInfo" class="quality-info">
                    <p>质检模式下的详细信息</p>
                </div>
            </div>
        </aside>
    </main>



    <!-- 隐藏的文件输入 -->
    <input type="file" id="imageFileInput" accept="image/*" multiple style="display: none;">
    <input type="file" id="folderInput" webkitdirectory style="display: none;">
    <input type="file" id="jsonFileInput" accept=".json" style="display: none;">

    <!-- 设置弹窗 -->
    <div id="settingsModal" class="modal">
        <div class="modal-content settings-modal">
            <div class="modal-header">
                <h3>设置</h3>
                <button class="modal-close" onclick="closeModal('settingsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-nav">
                    <button class="settings-tab active" data-tab="ocr">OCR配置</button>
                    <button class="settings-tab" data-tab="general">通用设置</button>
                </div>

                <div class="settings-content">
                    <div id="ocrSettings" class="settings-panel active">
                        <div class="setting-item">
                            <label>OCR模型:</label>
                            <select id="settingsOcrModel" class="setting-select">
                                <!-- 模型选项将通过JavaScript动态加载 -->
                            </select>
                        </div>

                        <div class="setting-item">
                            <label class="setting-checkbox">
                                <input type="checkbox" id="settingsUseTrueBatch">
                                <span class="checkbox-custom"></span>
                                启用批量OCR
                            </label>
                            <small class="setting-desc">将多张图片合并为一次API调用，大幅降低Token消耗</small>
                        </div>

                        <div class="setting-item">
                            <small class="setting-desc">
                                <strong>智能放大:</strong> 当图片最短边小于100像素时，自动等比例放大到100像素
                            </small>
                        </div>
                    </div>

                    <div id="generalSettings" class="settings-panel">
                        <div class="setting-item">
                            <label>界面主题:</label>
                            <select class="setting-select">
                                <option value="light">浅色主题</option>
                                <option value="dark">深色主题</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <label class="setting-checkbox">
                                <input type="checkbox" checked>
                                <span class="checkbox-custom"></span>
                                自动保存标注
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('settingsModal')">取消</button>
                <button class="btn btn-primary" onclick="saveSettings()">保存设置</button>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/coordinate-system.js"></script>
    <script src="js/image-manager.js"></script>
    <script src="js/annotation-manager.js"></script>
    <script src="js/data-manager.js"></script>
    <script src="js/quality-check.js"></script>
    <script src="js/workspace-manager.js"></script>
    <script src="js/ocr-service.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 全局弹窗控制函数
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                document.body.classList.remove('modal-open');
            }
        }

        // 保存设置
        function saveSettings() {
            const model = document.getElementById('settingsOcrModel').value;
            const useTrueBatch = document.getElementById('settingsUseTrueBatch').checked;

            // 触发设置保存事件
            document.dispatchEvent(new CustomEvent('saveSettings', {
                detail: {
                    model: model,
                    useTrueBatch: useTrueBatch
                }
            }));

            // 关闭弹窗
            closeModal('settingsModal');
        }

        // 设置标签页切换
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.settings-tab');
            const panels = document.querySelectorAll('.settings-panel');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;

                    // 切换标签激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 切换面板显示状态
                    panels.forEach(panel => {
                        panel.classList.remove('active');
                        if (panel.id === targetTab + 'Settings') {
                            panel.classList.add('active');
                        }
                    });
                });
            });
        });

        // 点击弹窗外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.classList.remove('modal-open');
                }
            });
        });
    </script>
</body>
</html>
