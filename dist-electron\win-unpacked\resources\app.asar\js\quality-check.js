/**
 * OCR标注工具 - 质检管理器
 * 负责质检模式、批量质检和质检报告功能
 */

class QualityCheckManager {
    constructor() {
        this.isQualityCheckMode = false;
        this.qualityCheckData = new Map(); // 存储每张图片的质检数据
        this.currentQualityData = null;
        this.batchQualityCheck = false;
        this.qualityCheckResults = [];
        
        this.callbacks = {
            onModeChange: [],
            onQualityDataLoad: [],
            onBatchStart: [],
            onBatchComplete: []
        };
    }

    /**
     * 添加回调函数
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 触发回调函数
     * @param {string} event 事件名称
     * @param {any} data 传递的数据
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    /**
     * 进入质检模式
     * @param {Object} jsonData JSON数据
     * @param {string} imageId 图片ID
     */
    enterQualityCheckMode(jsonData, imageId) {
        this.isQualityCheckMode = true;
        this.currentQualityData = this.parseQualityData(jsonData);
        
        if (imageId) {
            this.qualityCheckData.set(imageId, this.currentQualityData);
        }
        
        this.updateUI();
        this.trigger('onModeChange', { mode: 'quality-check', data: this.currentQualityData });
        this.trigger('onQualityDataLoad', this.currentQualityData);
    }

    /**
     * 退出质检模式
     */
    exitQualityCheckMode() {
        this.isQualityCheckMode = false;
        this.currentQualityData = null;
        this.updateUI();
        this.trigger('onModeChange', { mode: 'annotation', data: null });
    }

    /**
     * 切换质检模式
     * @param {Object} jsonData JSON数据（可选）
     * @param {string} imageId 图片ID（可选）
     */
    toggleQualityCheckMode(jsonData, imageId) {
        if (this.isQualityCheckMode) {
            this.exitQualityCheckMode();
        } else {
            if (jsonData) {
                this.enterQualityCheckMode(jsonData, imageId);
            } else {
                Utils.showNotification('请先加载标注数据', 'warning');
            }
        }
    }

    /**
     * 解析质检数据
     * @param {Object} jsonData JSON数据
     * @returns {Object} 质检数据
     */
    parseQualityData(jsonData) {
        const qualityData = {
            mainQuestions: [],
            subQuestions: [],
            answerAreas: [],
            imageAreas: [],
            statistics: {
                totalMainQuestions: 0,
                totalSubQuestions: 0,
                totalAnswerAreas: 0,
                totalImageAreas: 0,
                completedAnswerAreas: 0,
                emptyAnswerAreas: 0
            },
            issues: [],
            warnings: []
        };

        for (const [mainQuestionKey, mainQuestionData] of Object.entries(jsonData)) {
            if (!mainQuestionKey.startsWith('大题')) continue;

            // 解析大题
            const mainQuestion = {
                key: mainQuestionKey,
                coordinates: mainQuestionData['坐标'],
                questionType: mainQuestionData['题型'],
                content: mainQuestionData['题干文字'],
                hasImage: mainQuestionData['题目是否带配图'] === '是',
                imageAreas: []
            };

            qualityData.mainQuestions.push(mainQuestion);
            qualityData.statistics.totalMainQuestions++;

            // 检查大题完整性
            if (!mainQuestion.content) {
                qualityData.issues.push(`${mainQuestionKey}题干文字为空`);
            }
            if (!Utils.isValidCoordinates(mainQuestion.coordinates)) {
                qualityData.issues.push(`${mainQuestionKey}坐标格式无效`);
            }

            // 解析配图区域
            if (mainQuestionData['配图区域坐标']) {
                for (const [imageKey, imageCoords] of Object.entries(mainQuestionData['配图区域坐标'])) {
                    const imageArea = {
                        key: imageKey,
                        coordinates: imageCoords,
                        parentKey: mainQuestionKey
                    };
                    
                    mainQuestion.imageAreas.push(imageArea);
                    qualityData.imageAreas.push(imageArea);
                    qualityData.statistics.totalImageAreas++;

                    if (!Utils.isValidCoordinates(imageCoords)) {
                        qualityData.issues.push(`${imageKey}坐标格式无效`);
                    }
                }
            }

            // 解析小题
            if (mainQuestionData['小题']) {
                for (const [subQuestionKey, subQuestionData] of Object.entries(mainQuestionData['小题'])) {
                    const subQuestion = {
                        key: subQuestionKey,
                        parentKey: mainQuestionKey,
                        coordinates: subQuestionData['题干坐标'],
                        content: subQuestionData['题干内容'],
                        printWriteAttribute: subQuestionData['题干印刷手写属性'],
                        answerAreas: []
                    };

                    qualityData.subQuestions.push(subQuestion);
                    qualityData.statistics.totalSubQuestions++;

                    // 检查小题完整性
                    if (!subQuestion.content) {
                        qualityData.warnings.push(`${subQuestionKey}题干内容为空`);
                    }
                    if (!Utils.isValidCoordinates(subQuestion.coordinates)) {
                        qualityData.issues.push(`${subQuestionKey}题干坐标格式无效`);
                    }

                    // 解析答题区域
                    if (subQuestionData['答题区域']) {
                        for (const [answerAreaKey, answerAreaData] of Object.entries(subQuestionData['答题区域'])) {
                            const answerArea = {
                                key: answerAreaKey,
                                parentKey: subQuestionKey,
                                coordinates: answerAreaData['答题区域坐标'],
                                content: answerAreaData['答题区域内容'],
                                printWriteAttribute: answerAreaData['答题区域印刷手写属性'],
                                gradeResult: answerAreaData['批改结果'],
                                correctAnswer: answerAreaData['正确答案'],
                                answerExplanation: answerAreaData['答案解析']
                            };

                            subQuestion.answerAreas.push(answerArea);
                            qualityData.answerAreas.push(answerArea);
                            qualityData.statistics.totalAnswerAreas++;

                            // 统计完成情况
                            if (answerArea.content && answerArea.correctAnswer) {
                                qualityData.statistics.completedAnswerAreas++;
                            } else {
                                qualityData.statistics.emptyAnswerAreas++;
                                if (!answerArea.content) {
                                    qualityData.warnings.push(`${answerAreaKey}答题内容为空`);
                                }
                                if (!answerArea.correctAnswer) {
                                    qualityData.warnings.push(`${answerAreaKey}正确答案为空`);
                                }
                            }

                            // 检查坐标
                            if (!Utils.isValidCoordinates(answerArea.coordinates)) {
                                qualityData.issues.push(`${answerAreaKey}坐标格式无效`);
                            }
                        }
                    }
                }
            }
        }

        return qualityData;
    }

    /**
     * 生成质检报告
     * @param {Object} qualityData 质检数据
     * @returns {Object} 质检报告
     */
    generateQualityReport(qualityData) {
        const report = {
            summary: {
                totalIssues: qualityData.issues.length,
                totalWarnings: qualityData.warnings.length,
                completionRate: qualityData.statistics.totalAnswerAreas > 0 
                    ? (qualityData.statistics.completedAnswerAreas / qualityData.statistics.totalAnswerAreas * 100).toFixed(1)
                    : 0,
                qualityScore: this.calculateQualityScore(qualityData)
            },
            statistics: qualityData.statistics,
            issues: qualityData.issues,
            warnings: qualityData.warnings,
            recommendations: this.generateRecommendations(qualityData)
        };

        return report;
    }

    /**
     * 计算质量分数
     * @param {Object} qualityData 质检数据
     * @returns {number} 质量分数 (0-100)
     */
    calculateQualityScore(qualityData) {
        let score = 100;
        
        // 严重问题扣分
        score -= qualityData.issues.length * 10;
        
        // 警告扣分
        score -= qualityData.warnings.length * 5;
        
        // 完成度加分
        const completionRate = qualityData.statistics.totalAnswerAreas > 0 
            ? qualityData.statistics.completedAnswerAreas / qualityData.statistics.totalAnswerAreas
            : 0;
        score = score * completionRate;
        
        return Math.max(0, Math.min(100, Math.round(score)));
    }

    /**
     * 生成改进建议
     * @param {Object} qualityData 质检数据
     * @returns {Array} 建议列表
     */
    generateRecommendations(qualityData) {
        const recommendations = [];

        if (qualityData.statistics.emptyAnswerAreas > 0) {
            recommendations.push(`有${qualityData.statistics.emptyAnswerAreas}个答题区域内容不完整，建议补充完整`);
        }

        if (qualityData.issues.length > 0) {
            recommendations.push(`发现${qualityData.issues.length}个严重问题，需要立即修复`);
        }

        if (qualityData.warnings.length > 0) {
            recommendations.push(`有${qualityData.warnings.length}个警告项，建议检查和完善`);
        }

        if (qualityData.statistics.totalImageAreas === 0 && qualityData.mainQuestions.some(mq => mq.hasImage)) {
            recommendations.push('题目标记为带配图但未标注配图区域，请检查');
        }

        if (recommendations.length === 0) {
            recommendations.push('标注质量良好，无需特别改进');
        }

        return recommendations;
    }

    /**
     * 开始批量质检
     * @param {Array} imageList 图片列表
     * @param {Map} annotationDataMap 标注数据映射
     */
    startBatchQualityCheck(imageList, annotationDataMap) {
        this.batchQualityCheck = true;
        this.qualityCheckResults = [];
        
        this.trigger('onBatchStart', { total: imageList.length });

        imageList.forEach((image, index) => {
            const annotationData = annotationDataMap.get(image.id);
            if (annotationData) {
                const qualityData = this.parseQualityData(annotationData);
                const report = this.generateQualityReport(qualityData);
                
                this.qualityCheckResults.push({
                    imageId: image.id,
                    imageName: image.name,
                    qualityData: qualityData,
                    report: report,
                    index: index
                });
            }
        });

        this.trigger('onBatchComplete', this.qualityCheckResults);
        Utils.showNotification(`批量质检完成，共检查${this.qualityCheckResults.length}张图片`, 'success');
    }

    /**
     * 获取批量质检结果
     * @returns {Array} 质检结果列表
     */
    getBatchQualityResults() {
        return this.qualityCheckResults;
    }

    /**
     * 导出质检报告
     * @param {string} format 导出格式 ('json' | 'csv' | 'html')
     */
    exportQualityReport(format = 'json') {
        if (this.qualityCheckResults.length === 0) {
            Utils.showNotification('没有质检数据可导出', 'warning');
            return;
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        let filename = `质检报告_${timestamp}`;
        let content = '';
        let mimeType = 'application/json';

        switch (format) {
            case 'json':
                content = JSON.stringify(this.qualityCheckResults, null, 2);
                filename += '.json';
                mimeType = 'application/json';
                break;
                
            case 'csv':
                content = this.generateCSVReport();
                filename += '.csv';
                mimeType = 'text/csv';
                break;
                
            case 'html':
                content = this.generateHTMLReport();
                filename += '.html';
                mimeType = 'text/html';
                break;
                
            default:
                Utils.showNotification('不支持的导出格式', 'error');
                return;
        }

        Utils.file.download(content, filename, mimeType);
        Utils.showNotification('质检报告导出成功', 'success');
    }

    /**
     * 生成CSV格式报告
     * @returns {string} CSV内容
     */
    generateCSVReport() {
        const headers = [
            '图片名称', '质量分数', '完成率', '严重问题数', '警告数',
            '大题数', '小题数', '答题区域数', '配图区域数'
        ];
        
        const rows = [headers.join(',')];
        
        this.qualityCheckResults.forEach(result => {
            const row = [
                `"${result.imageName}"`,
                result.report.summary.qualityScore,
                result.report.summary.completionRate + '%',
                result.report.summary.totalIssues,
                result.report.summary.totalWarnings,
                result.report.statistics.totalMainQuestions,
                result.report.statistics.totalSubQuestions,
                result.report.statistics.totalAnswerAreas,
                result.report.statistics.totalImageAreas
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }

    /**
     * 生成HTML格式报告
     * @returns {string} HTML内容
     */
    generateHTMLReport() {
        const timestamp = new Date().toLocaleString('zh-CN');
        
        let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR标注质检报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .stat-label { font-size: 12px; color: #666; text-transform: uppercase; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .quality-score { font-weight: bold; }
        .score-excellent { color: #27ae60; }
        .score-good { color: #f39c12; }
        .score-poor { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>OCR标注质检报告</h1>
        <p>生成时间: ${timestamp}</p>
    </div>
    
    <div class="summary">
        <h2>总体概况</h2>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${this.qualityCheckResults.length}</div>
                <div class="stat-label">检查图片数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${this.calculateAverageScore()}</div>
                <div class="stat-label">平均质量分数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${this.calculateTotalIssues()}</div>
                <div class="stat-label">总问题数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${this.calculateAverageCompletion()}%</div>
                <div class="stat-label">平均完成率</div>
            </div>
        </div>
    </div>
    
    <h2>详细结果</h2>
    <table>
        <thead>
            <tr>
                <th>图片名称</th>
                <th>质量分数</th>
                <th>完成率</th>
                <th>问题数</th>
                <th>警告数</th>
                <th>大题数</th>
                <th>小题数</th>
                <th>答题区域数</th>
            </tr>
        </thead>
        <tbody>`;

        this.qualityCheckResults.forEach(result => {
            const scoreClass = result.report.summary.qualityScore >= 80 ? 'score-excellent' :
                              result.report.summary.qualityScore >= 60 ? 'score-good' : 'score-poor';
            
            html += `
            <tr>
                <td>${result.imageName}</td>
                <td class="quality-score ${scoreClass}">${result.report.summary.qualityScore}</td>
                <td>${result.report.summary.completionRate}%</td>
                <td>${result.report.summary.totalIssues}</td>
                <td>${result.report.summary.totalWarnings}</td>
                <td>${result.report.statistics.totalMainQuestions}</td>
                <td>${result.report.statistics.totalSubQuestions}</td>
                <td>${result.report.statistics.totalAnswerAreas}</td>
            </tr>`;
        });

        html += `
        </tbody>
    </table>
</body>
</html>`;

        return html;
    }

    /**
     * 计算平均分数
     * @returns {number} 平均分数
     */
    calculateAverageScore() {
        if (this.qualityCheckResults.length === 0) return 0;
        const total = this.qualityCheckResults.reduce((sum, result) => sum + result.report.summary.qualityScore, 0);
        return Math.round(total / this.qualityCheckResults.length);
    }

    /**
     * 计算总问题数
     * @returns {number} 总问题数
     */
    calculateTotalIssues() {
        return this.qualityCheckResults.reduce((sum, result) => 
            sum + result.report.summary.totalIssues + result.report.summary.totalWarnings, 0);
    }

    /**
     * 计算平均完成率
     * @returns {number} 平均完成率
     */
    calculateAverageCompletion() {
        if (this.qualityCheckResults.length === 0) return 0;
        const total = this.qualityCheckResults.reduce((sum, result) => 
            sum + parseFloat(result.report.summary.completionRate), 0);
        return Math.round(total / this.qualityCheckResults.length);
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        const body = document.body;
        const qualityCheckSection = document.querySelector('.quality-check-section');
        
        if (this.isQualityCheckMode) {
            body.classList.add('quality-check-mode');
            if (qualityCheckSection) {
                qualityCheckSection.style.display = 'block';
                this.updateQualityCheckInfo();
            }
        } else {
            body.classList.remove('quality-check-mode');
            if (qualityCheckSection) {
                qualityCheckSection.style.display = 'none';
            }
        }
    }

    /**
     * 更新质检信息显示
     */
    updateQualityCheckInfo() {
        const qualityCheckInfo = document.getElementById('qualityCheckInfo');
        if (!qualityCheckInfo || !this.currentQualityData) return;

        const report = this.generateQualityReport(this.currentQualityData);

        qualityCheckInfo.innerHTML = `
            <div class="quality-stats">
                <div class="quality-stat">
                    <div class="stat-number">${report.summary.qualityScore}</div>
                    <div class="stat-label">质量分数</div>
                </div>
                <div class="quality-stat">
                    <div class="stat-number">${report.summary.completionRate}%</div>
                    <div class="stat-label">完成率</div>
                </div>
                <div class="quality-stat">
                    <div class="stat-number">${report.summary.totalIssues}</div>
                    <div class="stat-label">严重问题</div>
                </div>
                <div class="quality-stat">
                    <div class="stat-number">${report.summary.totalWarnings}</div>
                    <div class="stat-label">警告</div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" style="width: ${report.summary.completionRate}%"></div>
            </div>

            ${this.generateDetailedQualityInfo()}
        `;
    }

    /**
     * 生成详细的质检信息
     * @returns {string} HTML字符串
     */
    generateDetailedQualityInfo() {
        if (!this.currentQualityData) return '';

        const report = this.generateQualityReport(this.currentQualityData);
        let html = '';

        // 题目详情
        if (this.currentQualityData.mainQuestions.length > 0) {
            html += '<div class="quality-section">';
            html += '<h4 class="section-title">📋 题目详情</h4>';

            this.currentQualityData.mainQuestions.forEach((mainQuestion, index) => {
                html += `
                    <div class="question-detail-card">
                        <div class="question-header">
                            <span class="question-number">第${index + 1}题</span>
                            <span class="question-type">${mainQuestion.questionType || '未设置'}</span>
                        </div>

                        <div class="question-content">
                            <div class="content-item">
                                <label>题干文字:</label>
                                <div class="content-text">${mainQuestion.content || '<span class="empty-content">未填写</span>'}</div>
                            </div>

                            <div class="content-item">
                                <label>配图情况:</label>
                                <span class="image-status ${mainQuestion.hasImage ? 'has-image' : 'no-image'}">
                                    ${mainQuestion.hasImage ? '✅ 有配图' : '❌ 无配图'}
                                </span>
                            </div>
                        </div>

                        ${this.generateSubQuestionsInfo(mainQuestion.key)}
                    </div>
                `;
            });

            html += '</div>';
        }

        // 统计信息
        html += `
            <div class="quality-section">
                <h4 class="section-title">📊 统计信息</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">大题数量</span>
                        <span class="stat-value">${report.statistics.totalMainQuestions}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">小题数量</span>
                        <span class="stat-value">${report.statistics.totalSubQuestions}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">答题区域</span>
                        <span class="stat-value">${report.statistics.totalAnswerAreas}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">配图区域</span>
                        <span class="stat-value">${report.statistics.totalImageAreas}</span>
                    </div>
                </div>
            </div>
        `;

        // 问题和警告
        if (report.issues.length > 0 || report.warnings.length > 0) {
            html += '<div class="quality-section">';
            html += '<h4 class="section-title">⚠️ 问题与警告</h4>';

            if (report.issues.length > 0) {
                html += '<div class="issues-container">';
                html += '<h5 class="issues-title">严重问题</h5>';
                html += '<ul class="issue-list">';
                report.issues.forEach(issue => {
                    html += `<li class="issue-item error">${issue}</li>`;
                });
                html += '</ul></div>';
            }

            if (report.warnings.length > 0) {
                html += '<div class="warnings-container">';
                html += '<h5 class="warnings-title">警告</h5>';
                html += '<ul class="warning-list">';
                report.warnings.forEach(warning => {
                    html += `<li class="warning-item warning">${warning}</li>`;
                });
                html += '</ul></div>';
            }

            html += '</div>';
        }

        // 改进建议
        if (report.recommendations && report.recommendations.length > 0) {
            html += `
                <div class="quality-section">
                    <h4 class="section-title">💡 改进建议</h4>
                    <ul class="recommendations-list">
                        ${report.recommendations.map(rec => `<li class="recommendation-item">${rec}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        return html;
    }

    /**
     * 生成小题信息
     * @param {string} mainQuestionKey 大题键名
     * @returns {string} HTML字符串
     */
    generateSubQuestionsInfo(mainQuestionKey) {
        const subQuestions = this.currentQualityData.subQuestions.filter(sq =>
            sq.parentKey === mainQuestionKey
        );

        if (subQuestions.length === 0) {
            return '<div class="sub-questions-info"><span class="no-sub-questions">无小题</span></div>';
        }

        let html = '<div class="sub-questions-info">';
        html += '<h6 class="sub-questions-title">小题详情</h6>';
        html += '<div class="sub-questions-list">';

        subQuestions.forEach((subQuestion, index) => {
            const answerAreas = this.currentQualityData.answerAreas.filter(aa =>
                aa.parentKey === subQuestion.key
            );

            html += `
                <div class="sub-question-item">
                    <div class="sub-question-header">
                        <span class="sub-question-number">小题${index + 1}</span>
                        <span class="answer-count">${answerAreas.length}个答题区域</span>
                    </div>

                    <div class="sub-question-details">
                        <div class="detail-item">
                            <label>题干内容:</label>
                            <div class="detail-content">${subQuestion.content || '<span class="empty-content">未填写</span>'}</div>
                        </div>

                        <div class="detail-item">
                            <label>印刷属性:</label>
                            <span class="print-attribute ${subQuestion.printWriteAttribute === '印刷' ? 'printed' : 'handwritten'}">
                                ${subQuestion.printWriteAttribute || '未设置'}
                            </span>
                        </div>
                    </div>

                    ${this.generateAnswerAreasInfo(answerAreas)}
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    /**
     * 生成答题区域信息
     * @param {Array} answerAreas 答题区域数组
     * @returns {string} HTML字符串
     */
    generateAnswerAreasInfo(answerAreas) {
        if (answerAreas.length === 0) {
            return '<div class="answer-areas-info"><span class="no-answer-areas">无答题区域</span></div>';
        }

        let html = '<div class="answer-areas-info">';
        html += '<h6 class="answer-areas-title">答题区域详情</h6>';
        html += '<div class="answer-areas-list">';

        answerAreas.forEach((answerArea, index) => {
            html += `
                <div class="answer-area-item">
                    <div class="answer-area-header">
                        <span class="answer-area-number">答题区域${index + 1}</span>
                        <span class="grade-result ${this.getGradeResultClass(answerArea.gradeResult)}">
                            ${answerArea.gradeResult || '未批改'}
                        </span>
                    </div>

                    <div class="answer-area-details">
                        <div class="detail-item">
                            <label>答题内容:</label>
                            <div class="detail-content answer-content">${answerArea.content || '<span class="empty-content">未填写</span>'}</div>
                        </div>

                        <div class="detail-item">
                            <label>印刷属性:</label>
                            <span class="print-attribute ${answerArea.printWriteAttribute === '印刷' ? 'printed' : 'handwritten'}">
                                ${answerArea.printWriteAttribute || '未设置'}
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>正确答案:</label>
                            <div class="detail-content correct-answer">${answerArea.correctAnswer || '<span class="empty-content">未填写</span>'}</div>
                        </div>

                        <div class="detail-item">
                            <label>答案解析:</label>
                            <div class="detail-content answer-explanation">${answerArea.answerExplanation || '<span class="empty-content">未填写</span>'}</div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div></div>';
        return html;
    }

    /**
     * 获取批改结果的CSS类名
     * @param {string} gradeResult 批改结果
     * @returns {string} CSS类名
     */
    getGradeResultClass(gradeResult) {
        switch (gradeResult) {
            case '正确':
                return 'correct';
            case '错误':
                return 'incorrect';
            case '部分正确':
                return 'partial';
            default:
                return 'ungraded';
        }
    }



    /**
     * 获取当前质检数据
     * @returns {Object|null} 当前质检数据
     */
    getCurrentQualityData() {
        return this.currentQualityData;
    }

    /**
     * 检查是否在质检模式
     * @returns {boolean} 是否在质检模式
     */
    isInQualityCheckMode() {
        return this.isQualityCheckMode;
    }

    /**
     * 清空质检数据
     */
    clear() {
        this.qualityCheckData.clear();
        this.currentQualityData = null;
        this.qualityCheckResults = [];
        this.batchQualityCheck = false;
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.clear();
        this.callbacks = {
            onModeChange: [],
            onQualityDataLoad: [],
            onBatchStart: [],
            onBatchComplete: []
        };
    }
}

// 导出质检管理器类
window.QualityCheckManager = QualityCheckManager;
