<template>
  <div id="app" class="ocr-app">
    <LayoutHeader />
    <div class="app-body">
      <LayoutSidebar />
      <ImageCanvas />
      <AnnotationPanel />
    </div>
    <StatusBar />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import LayoutHeader from '@/components/layout/LayoutHeader.vue'
import LayoutSidebar from '@/components/layout/LayoutSidebar.vue'
import ImageCanvas from '@/components/image/ImageCanvas.vue'
import AnnotationPanel from '@/components/annotation/AnnotationPanel.vue'
import StatusBar from '@/components/layout/StatusBar.vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

onMounted(() => {
  // 初始化应用
  appStore.initialize()
  
  // 监听Electron菜单事件
  if (window.electronAPI) {
    window.electronAPI.onMenuAction((action: string) => {
      appStore.handleMenuAction(action as any)
    })
  }
})

onUnmounted(() => {
  // 清理资源
  if (window.electronAPI) {
    window.electronAPI.removeAllListeners('menu-open-images')
    window.electronAPI.removeAllListeners('menu-open-folder')
    window.electronAPI.removeAllListeners('menu-save')
    window.electronAPI.removeAllListeners('menu-export-json')
  }
})
</script>

<style lang="scss">
.ocr-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  
  .app-body {
    flex: 1;
    display: flex;
    overflow: hidden;
  }
}
</style>
