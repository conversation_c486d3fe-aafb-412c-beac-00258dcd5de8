/* OCR标注工具 - 组件样式文件 */

/* 图标字体 */

.icon-folder::before { content: "📂"; }
.icon-prev::before { content: "◀"; }
.icon-next::before { content: "▶"; }
.icon-zoom-out::before { content: "🔍-"; }
.icon-zoom-in::before { content: "🔍+"; }
.icon-zoom-reset::before { content: "🔍"; }
.icon-main-question::before { content: "📝"; }
.icon-question-text::before { content: "📄"; }
.icon-sub-question::before { content: "📋"; }
.icon-answer-area::before { content: "✏️"; }
.icon-image-area::before { content: "🖼️"; }
.icon-clear::before { content: "🗑️"; }
.icon-save::before { content: "💾"; }
.icon-save-all::before { content: "💾📁"; }
.icon-memory::before { content: "🧠"; }
.icon-load::before { content: "📂"; }
.icon-clear-all::before { content: "🗑️"; }
.icon-export::before { content: "📤"; }
.icon-help::before { content: "❓"; }
.icon-folder::before { content: "📁"; }
.icon-refresh::before { content: "🔄"; }
.icon-visibility::before { content: "👁️"; }
.icon-ocr::before { content: "🔍📝"; }

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 90%;
    min-width: 600px;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
    animation: slideIn 0.3s ease;
}

.modal-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-md);
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: var(--text-muted);
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: var(--danger-color);
}

/* 快捷键说明样式 */
.shortcuts-help {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.shortcuts-section {
    margin-bottom: 30px;
}

.shortcuts-section h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.shortcut-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    transition: all 0.2s ease;
}

.shortcut-item:hover {
    background: #e9ecef;
    transform: translateX(4px);
}

.shortcut-item kbd {
    background: #343a40;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
    margin-right: 12px;
    min-width: 60px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.shortcut-item span {
    flex: 1;
    font-size: 14px;
    color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .shortcuts-grid {
        grid-template-columns: 1fr;
    }

    .shortcuts-help {
        padding: 15px;
    }

    .shortcut-item {
        padding: 10px 12px;
    }
}

/* 标注区域样式 */
.annotation-item {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.annotation-item:hover {
    background-color: #f8f9fa;
    border-color: var(--secondary-color);
}

.annotation-item.selected {
    background-color: rgba(52, 152, 219, 0.1);
    border-color: var(--secondary-color);
}

.annotation-item .annotation-type {
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: var(--spacing-xs);
}

.annotation-item .annotation-content {
    font-size: 13px;
    color: var(--text-color);
    line-height: 1.4;
}

.annotation-item .annotation-coords {
    font-size: 11px;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* 标注类型颜色 */
.annotation-item.main-question .annotation-type {
    color: var(--main-question-color);
}

.annotation-item.sub-question .annotation-type {
    color: var(--sub-question-color);
}

.annotation-item.answer-area .annotation-type {
    color: var(--answer-area-color);
}

.annotation-item.image-area .annotation-type {
    color: var(--image-area-color);
}

/* 编辑表单 */
.edit-form {
    background: #f8f9fa;
    padding: var(--spacing-md);
    border-radius: 4px;
    margin-top: var(--spacing-sm);
}

.edit-form .form-row {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.edit-form .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.edit-form .form-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    margin-top: var(--spacing-md);
}

.form-note {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: 0.9em;
    color: #6c757d;
}

.form-note p {
    margin: 0;
}

.form-note strong {
    color: #495057;
}

/* 质检模式样式 */

.quality-check-section {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
}

.quality-info {
    font-size: 13px;
    line-height: 1.6;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

/* 质检详细信息样式 */
.quality-section {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #fff;
}

.section-title {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.question-detail-card {
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    background: #f8f9fa;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.question-number {
    font-weight: 600;
    color: #007bff;
    font-size: 14px;
}

.question-type {
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: #495057;
}

.question-content {
    margin-bottom: 10px;
}

.content-item {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.content-item label {
    font-weight: 500;
    color: #6c757d;
    font-size: 12px;
    min-width: 60px;
    margin: 0;
}

.content-text {
    flex: 1;
    font-size: 12px;
    line-height: 1.4;
    word-break: break-all;
}

.empty-content {
    color: #dc3545;
    font-style: italic;
}

.image-status {
    font-size: 12px;
    font-weight: 500;
}

.has-image {
    color: #28a745;
}

.no-image {
    color: #6c757d;
}

.coordinates {
    font-family: monospace;
    font-size: 11px;
    color: #495057;
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
}

.invalid-coords {
    color: #dc3545;
    font-style: italic;
}

.sub-questions-info {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #dee2e6;
}

.sub-questions-title {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
}

.no-sub-questions {
    color: #6c757d;
    font-style: italic;
    font-size: 12px;
}

.sub-questions-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.sub-question-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
}

.sub-question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.sub-question-number {
    font-size: 11px;
    font-weight: 500;
    color: #28a745;
}

.answer-count {
    font-size: 11px;
    color: #6c757d;
}

.sub-question-details {
    margin-top: 8px;
}

.detail-item {
    margin-bottom: 6px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.detail-item label {
    font-weight: 500;
    color: #6c757d;
    font-size: 11px;
    min-width: 60px;
    margin: 0;
}

.detail-content {
    flex: 1;
    font-size: 11px;
    line-height: 1.4;
    word-break: break-all;
}

.print-attribute {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.print-attribute.printed {
    background: #e3f2fd;
    color: #1976d2;
}

.print-attribute.handwritten {
    background: #fff3e0;
    color: #f57c00;
}

.answer-areas-info {
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
}

.answer-areas-title {
    margin: 0 0 8px 0;
    font-size: 11px;
    font-weight: 600;
    color: #6c757d;
}

.no-answer-areas {
    color: #6c757d;
    font-style: italic;
    font-size: 11px;
}

.answer-areas-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.answer-area-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
}

.answer-area-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.answer-area-number {
    font-size: 11px;
    font-weight: 500;
    color: #495057;
}

.grade-result {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 500;
}

.grade-result.correct {
    background: #d4edda;
    color: #155724;
}

.grade-result.incorrect {
    background: #f8d7da;
    color: #721c24;
}

.grade-result.partial {
    background: #fff3cd;
    color: #856404;
}

.grade-result.ungraded {
    background: #e2e3e5;
    color: #6c757d;
}

.answer-area-details {
    margin-top: 6px;
}

.answer-content {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 4px 6px;
    min-height: 20px;
}

.correct-answer {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 3px;
    padding: 4px 6px;
    color: #155724;
}

.answer-explanation {
    background: #e2e3e5;
    border: 1px solid #ced4da;
    border-radius: 3px;
    padding: 4px 6px;
    color: #495057;
    font-style: italic;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.issues-container, .warnings-container {
    margin-bottom: 15px;
}

.issues-title, .warnings-title {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
}

.issues-title {
    color: #dc3545;
}

.warnings-title {
    color: #fd7e14;
}

.issue-list, .warning-list, .recommendations-list {
    margin: 0;
    padding-left: 20px;
}

.issue-item, .warning-item, .recommendation-item {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 4px;
}

.issue-item.error {
    color: #dc3545;
}

.warning-item.warning {
    color: #fd7e14;
}

.recommendation-item {
    color: #495057;
}

.quality-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.quality-stat {
    text-align: center;
    padding: var(--spacing-sm);
    background: white;
    border-radius: 4px;
    border: 1px solid #b3d9ff;
}

.quality-stat .stat-number {
    font-size: 18px;
    font-weight: bold;
    color: var(--secondary-color);
}

.quality-stat .stat-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.progress-fill {
    height: 100%;
    background: var(--success-color);
    transition: width 0.3s ease;
}

/* 通知消息 */
.notification {
    position: fixed;
    top: calc(var(--toolbar-height) + var(--spacing-sm));
    right: var(--spacing-md);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: 4px;
    color: white;
    font-size: 13px;
    z-index: 1500;
    animation: slideInRight 0.3s ease;
    max-width: 300px;
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--danger-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--secondary-color);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: var(--spacing-xs);
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar,
.questions-list::-webkit-scrollbar,
.images-list::-webkit-scrollbar {
    width: 6px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track,
.questions-list::-webkit-scrollbar-track,
.images-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb,
.questions-list::-webkit-scrollbar-thumb,
.images-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover,
.questions-list::-webkit-scrollbar-thumb:hover,
.images-list::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 选择状态 - 移除全局十字样式，只在canvas上应用 */

/* 禁用状态 */
.disabled {
    opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
}

/* 高亮状态 */
.highlight {
    animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
    from { opacity: 0.7; }
    to { opacity: 1; }
}

/* 错误状态 */
.error {
    border-color: var(--danger-color) !important;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
}

/* 成功状态 */
.success {
    border-color: var(--success-color) !important;
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
}

/* 工作区状态样式 */
.workspace-status {
    margin-top: 15px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    font-size: 12px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin: 4px 0;
}

.status-label {
    color: #6c757d;
    font-weight: 500;
}

.status-item span:last-child {
    color: #495057;
    font-weight: 600;
}

/* 层级内容样式 */
.hierarchy-content {
    max-height: 400px;
    overflow-y: auto;
}

.hierarchy-group {
    margin-bottom: 16px;
}

.hierarchy-group-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0 0 8px 0;
    padding: 4px 0;
    border-bottom: 1px solid #e9ecef;
}

/* 重写标注项样式以支持层级显示 */
.hierarchy-content .annotation-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.hierarchy-content .annotation-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hierarchy-content .annotation-item.main-question {
    border-left: 4px solid #e74c3c;
}

.hierarchy-content .annotation-item.sub-question {
    border-left: 4px solid #2ecc71;
}

.hierarchy-content .annotation-item.answer-area {
    border-left: 4px solid #9b59b6;
}

.hierarchy-content .annotation-item.image-area {
    border-left: 4px solid #f39c12;
}

.hierarchy-content .annotation-type {
    font-weight: 600;
    font-size: 13px;
    color: #495057;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hierarchy-content .annotation-content {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
    line-height: 1.4;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hierarchy-content .annotation-coords {
    font-size: 11px;
    color: #adb5bd;
    font-family: monospace;
}

.grade-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
}

.grade-badge.correct {
    background: #d4edda;
    color: #155724;
}

.grade-badge.incorrect {
    background: #f8d7da;
    color: #721c24;
}

.grade-badge.partial {
    background: #fff3cd;
    color: #856404;
}

/* OCR功能样式 */
.ocr-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.ocr-provider-selection,
.ocr-model-selection {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.ocr-provider-selection label,
.ocr-model-selection label {
    font-size: 0.9em;
    font-weight: 500;
    color: var(--text-secondary);
}

.ocr-batch-config,
.ocr-image-config {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin-bottom: var(--spacing-md);
}

.ocr-batch-config label,
.ocr-image-config label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    font-weight: 500;
    cursor: pointer;
    font-size: 0.9em;
}

.ocr-batch-config input[type="checkbox"],
.ocr-image-config input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
}

.config-hint {
    display: block;
    color: #6c757d;
    font-size: 0.8em;
    line-height: 1.4;
    margin-left: 24px;
}

/* 设置按钮样式 */
.btn-settings {
    background: #6c757d;
    color: white;
    border: 1px solid #6c757d;
}

.btn-settings:hover {
    background: #5a6268;
    border-color: #545b62;
}

.btn-settings .icon-settings::before {
    content: "⚙️";
}

/* OCR操作按钮样式 */
.ocr-action-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.btn-ocr {
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-ocr:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-ocr:active {
    transform: translateY(0);
}

.btn-ocr .ocr-icon {
    font-size: 16px;
}

.btn-ocr .ocr-text {
    flex: 1;
}

.ocr-loading {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.spinner {
    width: 16px;
    height: 16px;
    position: relative;
    display: inline-block;
}

.spinner::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-bottom-color: #ffffff;
    animation: spinPulse 1.2s linear infinite;
}

@keyframes spinPulse {
    0% {
        transform: rotate(0deg);
        border-width: 2px;
    }
    50% {
        transform: rotate(180deg);
        border-width: 1px;
    }
    100% {
        transform: rotate(360deg);
        border-width: 2px;
    }
}

/* 点状加载动画（备选方案） */
.spinner-dots {
    width: 16px;
    height: 16px;
    position: relative;
    display: inline-block;
}

.spinner-dots::before,
.spinner-dots::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #ffffff;
    animation: dotPulse 1.4s infinite ease-in-out;
}

.spinner-dots::before {
    left: 2px;
    animation-delay: -0.32s;
}

.spinner-dots::after {
    left: 10px;
    animation-delay: -0.16s;
}

@keyframes dotPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 脉冲加载动画 */
.spinner-wave {
    width: 12px;
    height: 12px;
    display: inline-block;
    position: relative;
}

.spinner-wave::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    margin: -4px 0 0 -4px;
    border-radius: 50%;
    background: #ffffff;
    animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(0.3);
        opacity: 1;
    }
    50% {
        transform: scale(1);
        opacity: 0.7;
    }
    100% {
        transform: scale(0.3);
        opacity: 1;
    }
}

/* 导航项OCR按钮样式 */
.nav-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.nav-item-main {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
}

.nav-ocr-btn {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 8px;
    flex-shrink: 0;
}

.nav-ocr-btn:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: scale(1.05);
}

.nav-ocr-btn:active {
    transform: scale(0.95);
}

.nav-ocr-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.nav-ocr-btn .ocr-icon {
    font-size: 12px;
}

.nav-ocr-btn .ocr-loading {
    display: none;
}

.nav-ocr-btn.loading .ocr-icon {
    display: none;
}

.nav-ocr-btn.loading .ocr-loading {
    display: flex;
}

.nav-ocr-btn .spinner-wave {
    width: 12px;
    height: 12px;
}

.nav-ocr-btn .spinner-wave::before {
    width: 6px;
    height: 6px;
    margin: -3px 0 0 -3px;
}

/* 设置弹窗样式 */
.settings-modal {
    width: 500px;
    max-width: 90vw;
}

.settings-nav {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 20px;
}

.settings-tab {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.settings-tab:hover {
    color: #495057;
    background: #f8f9fa;
}

.settings-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

.settings-content {
    min-height: 300px;
}

.settings-panel {
    display: none;
}

.settings-panel.active {
    display: block;
}

.setting-item {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f1f3f4;
}

.setting-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

.setting-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #495057;
    transition: border-color 0.15s ease;
}

.setting-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.setting-checkbox {
    display: flex !important;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid #ced4da;
    border-radius: 4px;
    background: white;
    position: relative;
    transition: all 0.2s ease;
}

.setting-checkbox input[type="checkbox"]:checked + .checkbox-custom {
    background: #007bff;
    border-color: #007bff;
}

.setting-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.setting-desc {
    display: block;
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
    margin-top: 6px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;
}

.modal-footer .btn {
    min-width: 80px;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
}

.form-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    font-size: 0.9em;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.ocr-btn {
    margin-top: var(--spacing-sm);
    width: 100%;
    justify-content: center;
    gap: var(--spacing-xs);
}

.ocr-progress {
    padding: var(--spacing-md);
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.9em;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #27ae60);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.ocr-results {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: white;
}

.results-list {
    padding: var(--spacing-sm);
}

.result-item {
    padding: var(--spacing-sm);
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.9em;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item.success {
    border-left: 3px solid #27ae60;
    background: #f8fff8;
}

.result-item.error {
    border-left: 3px solid #e74c3c;
    background: #fff8f8;
}

.result-header {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.result-content {
    color: var(--text-secondary);
    line-height: 1.4;
    word-break: break-word;
}

.result-meta {
    font-size: 0.8em;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
    display: flex;
    gap: var(--spacing-sm);
}

.ocr-section-hidden {
    display: none !important;
}
