import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { Annotation, AnnotationType, Point } from '@/types'
import { generateId, deepClone } from '@/utils/common'
import { useImageStore } from './image'

export const useAnnotationStore = defineStore('annotation', () => {
  // 状态
  const annotations = ref<Annotation[]>([])
  const selectedIds = ref<string[]>([])
  const clipboard = ref<Annotation[]>([])
  const history = ref<{ annotations: Annotation[]; timestamp: number }[]>([])
  const historyIndex = ref(-1)
  const maxHistorySize = 50

  // 获取图片store
  const imageStore = useImageStore()

  // 计算属性
  const selectedAnnotations = computed(() => 
    annotations.value.filter(ann => selectedIds.value.includes(ann.id))
  )

  const hasSelection = computed(() => selectedIds.value.length > 0)

  const canUndo = computed(() => historyIndex.value > 0)

  const canRedo = computed(() => historyIndex.value < history.value.length - 1)

  const annotationsByType = computed(() => {
    const result: Record<AnnotationType, Annotation[]> = {
      'main-question': [],
      'sub-question': [],
      'answer-area': [],
      'image-area': [],
      'question-text': []
    }
    
    annotations.value.forEach(ann => {
      if (result[ann.type]) {
        result[ann.type].push(ann)
      }
    })
    
    return result
  })

  const mainQuestions = computed(() => annotationsByType.value['main-question'])

  const getChildAnnotations = computed(() => (parentId: string) => 
    annotations.value.filter(ann => ann.parentId === parentId)
  )

  // 方法
  const addAnnotation = (annotation: Omit<Annotation, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAnnotation: Annotation = {
      ...annotation,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    annotations.value.push(newAnnotation)
    saveToHistory()
    updateCurrentImage()
    
    return newAnnotation
  }

  const updateAnnotation = (id: string, updates: Partial<Annotation>) => {
    const index = annotations.value.findIndex(ann => ann.id === id)
    if (index >= 0) {
      const updated = {
        ...annotations.value[index],
        ...updates,
        updatedAt: new Date()
      }
      annotations.value[index] = updated
      saveToHistory()
      updateCurrentImage()
      return updated
    }
    return null
  }

  const deleteAnnotation = (id: string) => {
    const index = annotations.value.findIndex(ann => ann.id === id)
    if (index >= 0) {
      const annotation = annotations.value[index]
      
      // 删除所有子标注
      const childIds = getChildAnnotations.value(id).map(child => child.id)
      childIds.forEach(childId => deleteAnnotation(childId))
      
      // 删除标注
      annotations.value.splice(index, 1)
      
      // 从选择中移除
      const selectedIndex = selectedIds.value.indexOf(id)
      if (selectedIndex >= 0) {
        selectedIds.value.splice(selectedIndex, 1)
      }
      
      saveToHistory()
      updateCurrentImage()
      return annotation
    }
    return null
  }

  const deleteSelected = () => {
    const toDelete = [...selectedIds.value]
    toDelete.forEach(id => deleteAnnotation(id))
    clearSelection()
  }

  const selectAnnotation = (id: string, addToSelection = false) => {
    if (addToSelection) {
      if (!selectedIds.value.includes(id)) {
        selectedIds.value.push(id)
      }
    } else {
      selectedIds.value = [id]
    }
  }

  const deselectAnnotation = (id: string) => {
    const index = selectedIds.value.indexOf(id)
    if (index >= 0) {
      selectedIds.value.splice(index, 1)
    }
  }

  const toggleSelection = (id: string) => {
    const index = selectedIds.value.indexOf(id)
    if (index >= 0) {
      selectedIds.value.splice(index, 1)
    } else {
      selectedIds.value.push(id)
    }
  }

  const selectAll = () => {
    selectedIds.value = annotations.value.map(ann => ann.id)
  }

  const clearSelection = () => {
    selectedIds.value = []
  }

  const selectByType = (type: AnnotationType) => {
    selectedIds.value = annotations.value
      .filter(ann => ann.type === type)
      .map(ann => ann.id)
  }

  const selectByParent = (parentId: string) => {
    selectedIds.value = annotations.value
      .filter(ann => ann.parentId === parentId)
      .map(ann => ann.id)
  }

  const copySelected = () => {
    clipboard.value = selectedAnnotations.value.map(ann => deepClone(ann))
  }

  const pasteAnnotations = (offset: Point = { x: 10, y: 10 }) => {
    if (clipboard.value.length === 0) return []

    const newAnnotations: Annotation[] = []
    const idMapping: Record<string, string> = {}

    // 第一遍：创建新ID映射
    clipboard.value.forEach(ann => {
      idMapping[ann.id] = generateId()
    })

    // 第二遍：创建新标注并更新父子关系
    clipboard.value.forEach(ann => {
      const newAnnotation: Annotation = {
        ...deepClone(ann),
        id: idMapping[ann.id],
        parentId: ann.parentId ? idMapping[ann.parentId] : undefined,
        coordinates: ann.coordinates.map(point => ({
          x: point.x + offset.x,
          y: point.y + offset.y
        })),
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      annotations.value.push(newAnnotation)
      newAnnotations.push(newAnnotation)
    })

    saveToHistory()
    updateCurrentImage()
    
    // 选择新粘贴的标注
    selectedIds.value = newAnnotations.map(ann => ann.id)
    
    return newAnnotations
  }

  const duplicateSelected = () => {
    copySelected()
    return pasteAnnotations()
  }

  const moveAnnotations = (ids: string[], offset: Point) => {
    ids.forEach(id => {
      const annotation = annotations.value.find(ann => ann.id === id)
      if (annotation) {
        annotation.coordinates = annotation.coordinates.map(point => ({
          x: point.x + offset.x,
          y: point.y + offset.y
        }))
        annotation.updatedAt = new Date()
      }
    })
    
    saveToHistory()
    updateCurrentImage()
  }

  const getAnnotationById = (id: string): Annotation | undefined => {
    return annotations.value.find(ann => ann.id === id)
  }

  const getAnnotationsByType = (type: AnnotationType): Annotation[] => {
    return annotations.value.filter(ann => ann.type === type)
  }

  const getAnnotationsByParent = (parentId: string): Annotation[] => {
    return annotations.value.filter(ann => ann.parentId === parentId)
  }

  const findAnnotationAt = (point: Point, tolerance = 5): Annotation | undefined => {
    // 从后往前查找（最后绘制的在最上层）
    for (let i = annotations.value.length - 1; i >= 0; i--) {
      const annotation = annotations.value[i]
      if (isPointNearAnnotation(point, annotation, tolerance)) {
        return annotation
      }
    }
    return undefined
  }

  const isPointNearAnnotation = (point: Point, annotation: Annotation, tolerance: number): boolean => {
    // 简化实现：检查点是否在标注的边界框内
    if (annotation.coordinates.length === 0) return false
    
    const minX = Math.min(...annotation.coordinates.map(p => p.x)) - tolerance
    const maxX = Math.max(...annotation.coordinates.map(p => p.x)) + tolerance
    const minY = Math.min(...annotation.coordinates.map(p => p.y)) - tolerance
    const maxY = Math.max(...annotation.coordinates.map(p => p.y)) + tolerance
    
    return point.x >= minX && point.x <= maxX && point.y >= minY && point.y <= maxY
  }

  const saveToHistory = () => {
    // 移除当前位置之后的历史记录
    if (historyIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, historyIndex.value + 1)
    }
    
    // 添加新的历史记录
    history.value.push({
      annotations: deepClone(annotations.value),
      timestamp: Date.now()
    })
    
    // 限制历史记录大小
    if (history.value.length > maxHistorySize) {
      history.value.shift()
    } else {
      historyIndex.value++
    }
  }

  const undo = () => {
    if (canUndo.value) {
      historyIndex.value--
      annotations.value = deepClone(history.value[historyIndex.value].annotations)
      clearSelection()
      updateCurrentImage()
    }
  }

  const redo = () => {
    if (canRedo.value) {
      historyIndex.value++
      annotations.value = deepClone(history.value[historyIndex.value].annotations)
      clearSelection()
      updateCurrentImage()
    }
  }

  const clearHistory = () => {
    history.value = []
    historyIndex.value = -1
  }

  const loadAnnotations = (newAnnotations: Annotation[]) => {
    annotations.value = newAnnotations
    clearSelection()
    clearHistory()
    saveToHistory()
    updateCurrentImage()
  }

  const clearAnnotations = () => {
    annotations.value = []
    clearSelection()
    clearHistory()
    updateCurrentImage()
  }

  const updateCurrentImage = () => {
    const currentImage = imageStore.currentImage
    if (currentImage) {
      currentImage.annotations = deepClone(annotations.value)
      currentImage.updatedAt = new Date()
    }
  }

  const getNextNumber = (type: AnnotationType, parentId?: string): number => {
    let existingNumbers: number[]
    
    if (parentId) {
      // 获取同一父级下同类型标注的编号
      existingNumbers = annotations.value
        .filter(ann => ann.type === type && ann.parentId === parentId)
        .map(ann => ann.attributes.number || 0)
        .filter(num => num > 0)
    } else {
      // 获取同类型标注的编号
      existingNumbers = annotations.value
        .filter(ann => ann.type === type && !ann.parentId)
        .map(ann => ann.attributes.number || 0)
        .filter(num => num > 0)
    }
    
    if (existingNumbers.length === 0) return 1
    
    const maxNumber = Math.max(...existingNumbers)
    return maxNumber + 1
  }

  const renumberAnnotations = (type: AnnotationType, parentId?: string) => {
    const targetAnnotations = annotations.value
      .filter(ann => ann.type === type && ann.parentId === parentId)
      .sort((a, b) => {
        // 按坐标排序（从上到下，从左到右）
        if (Math.abs(a.coordinates[0]?.y - b.coordinates[0]?.y) < 10) {
          return a.coordinates[0]?.x - b.coordinates[0]?.x
        }
        return a.coordinates[0]?.y - b.coordinates[0]?.y
      })
    
    targetAnnotations.forEach((ann, index) => {
      ann.attributes.number = index + 1
      ann.updatedAt = new Date()
    })
    
    saveToHistory()
    updateCurrentImage()
  }

  return {
    // 状态
    annotations: readonly(annotations),
    selectedIds: readonly(selectedIds),
    clipboard: readonly(clipboard),
    
    // 计算属性
    selectedAnnotations,
    hasSelection,
    canUndo,
    canRedo,
    annotationsByType,
    mainQuestions,
    getChildAnnotations,
    
    // 方法
    addAnnotation,
    updateAnnotation,
    deleteAnnotation,
    deleteSelected,
    selectAnnotation,
    deselectAnnotation,
    toggleSelection,
    selectAll,
    clearSelection,
    selectByType,
    selectByParent,
    copySelected,
    pasteAnnotations,
    duplicateSelected,
    moveAnnotations,
    getAnnotationById,
    getAnnotationsByType,
    getAnnotationsByParent,
    findAnnotationAt,
    undo,
    redo,
    loadAnnotations,
    clearAnnotations,
    getNextNumber,
    renumberAnnotations
  }
})
