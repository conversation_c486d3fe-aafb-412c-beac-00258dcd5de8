/**
 * OCR标注工具 - 工具函数库
 * 提供通用的工具函数和辅助方法
 */

class Utils {
    /**
     * 生成唯一ID
     * @returns {string} 唯一标识符
     */
    static generateId() {
        return 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 时间限制（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深拷贝对象
     * @param {any} obj 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => Utils.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 检查文件类型是否为图片
     * @param {File} file 文件对象
     * @returns {boolean} 是否为图片
     */
    static isImageFile(file) {
        const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
        return imageTypes.includes(file.type.toLowerCase());
    }

    /**
     * 获取文件扩展名
     * @param {string} filename 文件名
     * @returns {string} 扩展名
     */
    static getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
    }

    /**
     * 显示通知消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, warning, info)
     * @param {number} duration 显示时长（毫秒）
     */
    static showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideInRight 0.3s ease reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    /**
     * 显示确认对话框
     * @param {string} message 确认消息
     * @param {Function} onConfirm 确认回调
     * @param {Function} onCancel 取消回调
     */
    static showConfirm(message, onConfirm, onCancel) {
        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modalBody');
        
        modalBody.innerHTML = `
            <div style="text-align: center; padding: 20px;">
                <p style="margin-bottom: 20px; font-size: 16px;">${message}</p>
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button id="confirmBtn" class="btn btn-primary">确认</button>
                    <button id="cancelBtn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        `;
        
        modal.classList.add('show');
        
        document.getElementById('confirmBtn').onclick = () => {
            modal.classList.remove('show');
            if (onConfirm) onConfirm();
        };
        
        document.getElementById('cancelBtn').onclick = () => {
            modal.classList.remove('show');
            if (onCancel) onCancel();
        };
        
        document.querySelector('.modal-close').onclick = () => {
            modal.classList.remove('show');
            if (onCancel) onCancel();
        };
    }

    /**
     * 计算两点之间的距离
     * @param {Object} point1 第一个点 {x, y}
     * @param {Object} point2 第二个点 {x, y}
     * @returns {number} 距离
     */
    static getDistance(point1, point2) {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 检查点是否在矩形内
     * @param {Object} point 点坐标 {x, y}
     * @param {Object} rect 矩形 {x, y, width, height}
     * @returns {boolean} 是否在矩形内
     */
    static isPointInRect(point, rect) {
        return point.x >= rect.x && 
               point.x <= rect.x + rect.width && 
               point.y >= rect.y && 
               point.y <= rect.y + rect.height;
    }

    /**
     * 检查两个矩形是否重叠
     * @param {Object} rect1 第一个矩形
     * @param {Object} rect2 第二个矩形
     * @returns {boolean} 是否重叠
     */
    static isRectOverlap(rect1, rect2) {
        return !(rect1.x + rect1.width < rect2.x || 
                rect2.x + rect2.width < rect1.x || 
                rect1.y + rect1.height < rect2.y || 
                rect2.y + rect2.height < rect1.y);
    }

    /**
     * 限制数值在指定范围内
     * @param {number} value 数值
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {number} 限制后的数值
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    /**
     * 格式化坐标显示
     * @param {Array} coordinates 坐标数组 [[x1, y1], [x2, y2]]
     * @returns {string} 格式化后的坐标字符串
     */
    static formatCoordinates(coordinates) {
        if (!coordinates || coordinates.length !== 2) return '无效坐标';
        const [start, end] = coordinates;
        return `(${Math.round(start[0])}, ${Math.round(start[1])}) - (${Math.round(end[0])}, ${Math.round(end[1])})`;
    }

    /**
     * 验证坐标格式
     * @param {Array} coordinates 坐标数组
     * @returns {boolean} 是否有效
     */
    static isValidCoordinates(coordinates) {
        return Array.isArray(coordinates) && 
               coordinates.length === 2 && 
               Array.isArray(coordinates[0]) && 
               Array.isArray(coordinates[1]) && 
               coordinates[0].length === 2 && 
               coordinates[1].length === 2 && 
               coordinates.every(coord => coord.every(num => typeof num === 'number' && !isNaN(num)));
    }

    /**
     * 本地存储操作
     */
    static storage = {
        /**
         * 保存数据到本地存储
         * @param {string} key 键名
         * @param {any} data 数据
         */
        save(key, data) {
            try {
                localStorage.setItem(key, JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('保存到本地存储失败:', error);
                return false;
            }
        },

        /**
         * 从本地存储读取数据
         * @param {string} key 键名
         * @param {any} defaultValue 默认值
         * @returns {any} 读取的数据
         */
        load(key, defaultValue = null) {
            try {
                const data = localStorage.getItem(key);
                return data ? JSON.parse(data) : defaultValue;
            } catch (error) {
                console.error('从本地存储读取失败:', error);
                return defaultValue;
            }
        },

        /**
         * 删除本地存储数据
         * @param {string} key 键名
         */
        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('删除本地存储失败:', error);
                return false;
            }
        },

        /**
         * 清空本地存储
         */
        clear() {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('清空本地存储失败:', error);
                return false;
            }
        }
    };

    /**
     * 文件操作工具
     */
    static file = {
        /**
         * 下载文件
         * @param {string} content 文件内容
         * @param {string} filename 文件名
         * @param {string} mimeType MIME类型
         */
        download(content, filename, mimeType = 'application/json') {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        },

        /**
         * 读取文件内容
         * @param {File} file 文件对象
         * @returns {Promise<string>} 文件内容
         */
        readAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = e => reject(e);
                reader.readAsText(file);
            });
        },

        /**
         * 读取图片文件
         * @param {File} file 图片文件
         * @returns {Promise<string>} 图片URL
         */
        readAsDataURL(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = e => reject(e);
                reader.readAsDataURL(file);
            });
        }
    };

    /**
     * 显示快捷键说明
     */
    static showShortcutsHelp() {
        const modal = document.getElementById('modal');
        const modalBody = document.getElementById('modalBody');

        modalBody.innerHTML = `
            <div class="shortcuts-help">
                <h2 style="text-align: center; color: #333; margin-bottom: 30px;">
                    ⌨️ 快捷键说明
                </h2>

                <div class="shortcuts-section">
                    <h3 style="color: #555; border-bottom: 2px solid #3498db; padding-bottom: 8px;">
                        🏷️ 标注工具选择
                    </h3>
                    <div class="shortcuts-grid">
                        <div class="shortcut-item">
                            <kbd>1</kbd>
                            <span>选择大题工具</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>2</kbd>
                            <span>选择题干文字工具</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>3</kbd>
                            <span>选择小题工具</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>4</kbd>
                            <span>选择答题区域工具</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>5</kbd>
                            <span>选择配图区域工具</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Esc</kbd>
                            <span>取消选择标注工具</span>
                        </div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h3 style="color: #555; border-bottom: 2px solid #e74c3c; padding-bottom: 8px;">
                        🖼️ 图片导航
                    </h3>
                    <div class="shortcuts-grid">
                        <div class="shortcut-item">
                            <kbd>←</kbd>
                            <span>上一张图片</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>→</kbd>
                            <span>下一张图片</span>
                        </div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h3 style="color: #555; border-bottom: 2px solid #2ecc71; padding-bottom: 8px;">
                        🔍 图片缩放与移动
                    </h3>
                    <div class="shortcuts-grid">
                        <div class="shortcut-item">
                            <kbd>+</kbd>
                            <span>放大图片</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>-</kbd>
                            <span>缩小图片</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>0</kbd>
                            <span>重置缩放和位置</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>滚轮</kbd>
                            <span>以鼠标位置为中心缩放</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>右键拖拽</kbd>
                            <span>移动图片</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>Ctrl + 左键拖拽</kbd>
                            <span>移动图片</span>
                        </div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h3 style="color: #555; border-bottom: 2px solid #9b59b6; padding-bottom: 8px;">
                        🔧 其他操作
                    </h3>
                    <div class="shortcuts-grid">
                        <div class="shortcut-item">
                            <kbd>Q</kbd>
                            <span>显示/隐藏标注</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>W</kbd>
                            <span>切换标注模式和质检模式</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>D / Del</kbd>
                            <span>删除选中的标注框</span>
                        </div>
                        <div class="shortcut-item">
                            <kbd>F1</kbd>
                            <span>显示快捷键说明</span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                    <p style="color: #666; font-size: 14px;">
                        💡 提示：在输入框中输入文字时，快捷键会被禁用
                    </p>
                </div>
            </div>
        `;

        modal.classList.add('show');

        // 点击关闭按钮或背景关闭模态框
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.onclick = () => modal.classList.remove('show');

        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
            }
        };
    }
}

// 导出工具类
window.Utils = Utils;
