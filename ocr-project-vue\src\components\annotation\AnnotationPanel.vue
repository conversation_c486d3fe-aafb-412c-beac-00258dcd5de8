<template>
  <div class="annotation-panel">
    <div class="panel-header">
      <h3>标注列表</h3>
      <div class="header-actions">
        <el-button-group size="small">
          <el-button @click="selectAll" :icon="Select">全选</el-button>
          <el-button @click="clearSelection" :icon="Close">清除</el-button>
          <el-button 
            @click="deleteSelected" 
            type="danger" 
            :icon="Delete"
            :disabled="!hasSelection"
          >
            删除
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="panel-content scrollbar">
      <div class="annotation-tree">
        <!-- 主题列表 -->
        <div 
          v-for="mainQuestion in mainQuestions" 
          :key="mainQuestion.id"
          class="main-question-item"
        >
          <div 
            class="annotation-item main-question"
            :class="{ selected: isSelected(mainQuestion.id) }"
            @click="selectAnnotation(mainQuestion.id, $event)"
          >
            <div class="item-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-title">
                大题 {{ mainQuestion.attributes.number || '' }}
              </div>
              <div class="item-meta">
                {{ getChildCount(mainQuestion.id) }} 个子项
              </div>
            </div>
            <div class="item-actions">
              <el-button 
                @click.stop="editAnnotation(mainQuestion)"
                size="small"
                :icon="Edit"
                circle
              />
            </div>
          </div>
          
          <!-- 子题列表 -->
          <div class="sub-items" v-if="getChildAnnotations(mainQuestion.id).length > 0">
            <div 
              v-for="subItem in getChildAnnotations(mainQuestion.id)"
              :key="subItem.id"
              class="annotation-item sub-item"
              :class="{ 
                selected: isSelected(subItem.id),
                [subItem.type]: true 
              }"
              @click="selectAnnotation(subItem.id, $event)"
            >
              <div class="item-icon">
                <el-icon>
                  <component :is="getAnnotationIcon(subItem.type)" />
                </el-icon>
              </div>
              <div class="item-content">
                <div class="item-title">
                  {{ getAnnotationLabel(subItem) }}
                </div>
                <div class="item-meta">
                  {{ formatCoordinates(subItem.coordinates) }}
                </div>
              </div>
              <div class="item-actions">
                <el-button 
                  @click.stop="editAnnotation(subItem)"
                  size="small"
                  :icon="Edit"
                  circle
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 独立标注（没有父级的非主题标注） -->
        <div 
          v-for="annotation in independentAnnotations"
          :key="annotation.id"
          class="annotation-item independent"
          :class="{ 
            selected: isSelected(annotation.id),
            [annotation.type]: true 
          }"
          @click="selectAnnotation(annotation.id, $event)"
        >
          <div class="item-icon">
            <el-icon>
              <component :is="getAnnotationIcon(annotation.type)" />
            </el-icon>
          </div>
          <div class="item-content">
            <div class="item-title">
              {{ getAnnotationLabel(annotation) }}
            </div>
            <div class="item-meta">
              {{ formatCoordinates(annotation.coordinates) }}
            </div>
          </div>
          <div class="item-actions">
            <el-button 
              @click.stop="editAnnotation(annotation)"
              size="small"
              :icon="Edit"
              circle
            />
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="annotations.length === 0" class="empty-state">
          <el-icon><Document /></el-icon>
          <p>暂无标注</p>
          <p class="hint">使用左侧工具栏开始标注</p>
        </div>
      </div>
    </div>
    
    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="`编辑${getAnnotationTypeLabel(editingAnnotation?.type)}`"
      width="400px"
    >
      <el-form 
        v-if="editingAnnotation"
        :model="editForm"
        label-width="80px"
        size="small"
      >
        <el-form-item label="编号">
          <el-input-number 
            v-model="editForm.number"
            :min="1"
            :max="999"
          />
        </el-form-item>
        <el-form-item label="内容">
          <el-input 
            v-model="editForm.content"
            type="textarea"
            :rows="3"
            placeholder="请输入标注内容"
          />
        </el-form-item>
        <el-form-item label="分数" v-if="editingAnnotation.type === 'sub-question'">
          <el-input-number 
            v-model="editForm.score"
            :min="0"
            :max="100"
            :precision="1"
          />
        </el-form-item>
        <el-form-item label="难度" v-if="editingAnnotation.type === 'sub-question'">
          <el-select v-model="editForm.difficulty" placeholder="请选择难度">
            <el-option label="简单" value="easy" />
            <el-option label="中等" value="medium" />
            <el-option label="困难" value="hard" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAnnotation">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Select, Close, Delete, Edit, Document, List, 
  Picture, ChatLineRound
} from '@element-plus/icons-vue'
import { useAnnotationStore } from '@/stores/annotation'
import type { Annotation, AnnotationType, Point } from '@/types'

// Store
const annotationStore = useAnnotationStore()

// 响应式数据
const editDialogVisible = ref(false)
const editingAnnotation = ref<Annotation>()
const editForm = ref({
  number: 1,
  content: '',
  score: 0,
  difficulty: 'medium'
})

// 计算属性
const annotations = computed(() => annotationStore.annotations)
const selectedIds = computed(() => annotationStore.selectedIds)
const hasSelection = computed(() => annotationStore.hasSelection)
const mainQuestions = computed(() => annotationStore.mainQuestions)

const independentAnnotations = computed(() => 
  annotations.value.filter(ann => 
    ann.type !== 'main-question' && !ann.parentId
  )
)

const getChildAnnotations = computed(() => annotationStore.getChildAnnotations)

// 方法
const isSelected = (id: string): boolean => {
  return selectedIds.value.includes(id)
}

const selectAnnotation = (id: string, event: MouseEvent) => {
  const addToSelection = event.ctrlKey || event.metaKey
  annotationStore.selectAnnotation(id, addToSelection)
}

const selectAll = () => {
  annotationStore.selectAll()
}

const clearSelection = () => {
  annotationStore.clearSelection()
}

const deleteSelected = () => {
  annotationStore.deleteSelected()
}

const getChildCount = (parentId: string): number => {
  return getChildAnnotations.value(parentId).length
}

const getAnnotationIcon = (type: AnnotationType) => {
  const icons = {
    'main-question': Document,
    'sub-question': List,
    'answer-area': Edit,
    'image-area': Picture,
    'question-text': ChatLineRound
  }
  return icons[type] || Document
}

const getAnnotationLabel = (annotation: Annotation): string => {
  const typeLabels = {
    'main-question': '大题',
    'sub-question': '小题',
    'answer-area': '答题区域',
    'image-area': '配图区域',
    'question-text': '题干文字'
  }
  
  const typeLabel = typeLabels[annotation.type] || ''
  const number = annotation.attributes.number || ''
  
  return number ? `${typeLabel} ${number}` : typeLabel
}

const getAnnotationTypeLabel = (type?: AnnotationType): string => {
  const labels = {
    'main-question': '大题',
    'sub-question': '小题',
    'answer-area': '答题区域',
    'image-area': '配图区域',
    'question-text': '题干文字'
  }
  return type ? labels[type] || '' : ''
}

const formatCoordinates = (coordinates: readonly Point[]): string => {
  if (coordinates.length === 0) return ''
  if (coordinates.length === 1) {
    return `(${Math.round(coordinates[0].x)}, ${Math.round(coordinates[0].y)})`
  }
  
  const minX = Math.min(...coordinates.map(p => p.x))
  const minY = Math.min(...coordinates.map(p => p.y))
  const maxX = Math.max(...coordinates.map(p => p.x))
  const maxY = Math.max(...coordinates.map(p => p.y))
  
  return `${Math.round(maxX - minX)}×${Math.round(maxY - minY)}`
}

const editAnnotation = (annotation: Annotation) => {
  editingAnnotation.value = annotation
  editForm.value = {
    number: annotation.attributes.number || 1,
    content: annotation.attributes.content || '',
    score: annotation.attributes.score || 0,
    difficulty: annotation.attributes.difficulty || 'medium'
  }
  editDialogVisible.value = true
}

const saveAnnotation = () => {
  if (!editingAnnotation.value) return
  
  annotationStore.updateAnnotation(editingAnnotation.value.id, {
    attributes: {
      ...editingAnnotation.value.attributes,
      ...editForm.value
    }
  })
  
  editDialogVisible.value = false
  editingAnnotation.value = undefined
}
</script>

<style lang="scss" scoped>
.annotation-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
  
  .panel-header {
    @include flex-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      margin: 0;
      font-size: var(--font-size-md);
      color: var(--text-color);
    }
  }
  
  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-sm);
  }
}

.annotation-tree {
  .main-question-item {
    margin-bottom: var(--spacing-md);
    
    .sub-items {
      margin-left: var(--spacing-lg);
      margin-top: var(--spacing-xs);
      
      .annotation-item {
        margin-bottom: var(--spacing-xs);
      }
    }
  }
  
  .annotation-item {
    @include flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-fast);
    border: 1px solid transparent;
    
    &:hover {
      background: var(--bg-color);
    }
    
    &.selected {
      background: var(--secondary-color);
      color: white;
      border-color: var(--secondary-color);
    }
    
    &.main-question {
      background: var(--bg-color);
      border: 1px solid var(--border-color);
      
      &.selected {
        background: var(--primary-color);
        border-color: var(--primary-color);
      }
    }
    
    .item-icon {
      width: 24px;
      height: 24px;
      @include flex-center;
      border-radius: var(--border-radius-small);
      background: var(--bg-light);
      
      .el-icon {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }
    
    .item-content {
      flex: 1;
      min-width: 0;
      
      .item-title {
        font-size: var(--font-size-sm);
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .item-meta {
        font-size: var(--font-size-xs);
        color: var(--text-light);
        margin-top: 2px;
      }
    }
    
    .item-actions {
      opacity: 0;
      transition: var(--transition-fast);
    }
    
    &:hover .item-actions {
      opacity: 1;
    }
    
    &.selected .item-actions {
      opacity: 1;
    }
  }
  
  .empty-state {
    @include flex-center;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
    
    .el-icon {
      font-size: 48px;
      color: var(--text-light);
    }
    
    .hint {
      font-size: var(--font-size-xs);
      color: var(--text-light);
    }
  }
}

// 标注类型颜色
.annotation-item {
  &.main-question .item-icon {
    background: rgba(24, 144, 255, 0.1);
    .el-icon { color: #1890ff; }
  }
  
  &.sub-question .item-icon {
    background: rgba(82, 196, 26, 0.1);
    .el-icon { color: #52c41a; }
  }
  
  &.answer-area .item-icon {
    background: rgba(250, 140, 22, 0.1);
    .el-icon { color: #fa8c16; }
  }
  
  &.image-area .item-icon {
    background: rgba(114, 46, 209, 0.1);
    .el-icon { color: #722ed1; }
  }
  
  &.question-text .item-icon {
    background: rgba(235, 47, 150, 0.1);
    .el-icon { color: #eb2f96; }
  }
}
</style>
