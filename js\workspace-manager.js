/**
 * OCR标注工具 - 工作区管理器
 * 负责工作区文件夹的管理、JSON文件的读写
 */

class WorkspaceManager {
    constructor() {
        this.workspacePath = null;
        this.imagesFolderPath = null;
        this.jsonFolderPath = null;
        this.jsonFiles = new Map(); // 存储已加载的JSON文件
        this.isWorkspaceLoaded = false;
        this.isElectron = window.isElectron || false;

        this.callbacks = {
            onWorkspaceLoad: [],
            onJsonSave: [],
            onJsonLoad: []
        };
    }

    /**
     * 添加回调函数
     * @param {string} event 事件名称
     * @param {Function} callback 回调函数
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 触发回调函数
     * @param {string} event 事件名称
     * @param {any} data 传递的数据
     */
    trigger(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => callback(data));
        }
    }

    /**
     * 检查是否支持文件系统API
     * @returns {boolean} 是否支持
     */
    isFileSystemAPISupported() {
        return this.isElectron || ('showDirectoryPicker' in window && 'showSaveFilePicker' in window);
    }

    /**
     * 打开工作区文件夹
     */
    async openWorkspace() {
        if (!this.isFileSystemAPISupported()) {
            Utils.showNotification('当前环境不支持文件系统操作', 'error');
            return false;
        }

        try {
            let workspacePath;

            if (this.isElectron) {
                // Electron环境下使用IPC
                workspacePath = await window.electronAPI.selectWorkspaceFolder();
                if (!workspacePath) {
                    return false; // 用户取消选择
                }
                this.workspacePath = workspacePath;
            } else {
                // 浏览器环境下使用File System API
                this.workspaceHandle = await window.showDirectoryPicker({
                    mode: 'readwrite'
                });
                this.workspacePath = this.workspaceHandle.name;
            }

            // 查找图片文件夹和JSON文件夹
            await this.findImageFolder();
            await this.findJsonFolder();

            // 加载现有的JSON文件
            await this.loadExistingJsonFiles();

            this.isWorkspaceLoaded = true;

            const workspaceName = this.isElectron ?
                this.workspacePath.split(/[/\\]/).pop() :
                this.workspaceHandle.name;

            this.trigger('onWorkspaceLoad', {
                workspaceName: workspaceName,
                imageCount: await this.countImagesInFolder(),
                jsonCount: this.jsonFiles.size
            });

            Utils.showNotification(`工作区已打开: ${workspaceName}`, 'success');
            return true;

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('打开工作区失败:', error);
                Utils.showNotification('打开工作区失败: ' + error.message, 'error');
            }
            return false;
        }
    }

    /**
     * 查找图片文件夹
     */
    async findImageFolder() {
        const possibleNames = ['2、原始图片', '原始图片', 'images', 'pics', '图片'];

        if (this.isElectron) {
            // Electron环境
            const items = await window.electronAPI.readFolder(this.workspacePath);
            for (const item of items) {
                if (item.isDirectory && possibleNames.includes(item.name)) {
                    this.imagesFolderPath = item.path;
                    console.log(`找到图片文件夹: ${item.name}`);
                    return;
                }
            }

            // 如果没找到，尝试创建
            const imageFolderPath = this.workspacePath + (this.workspacePath.endsWith('/') || this.workspacePath.endsWith('\\') ? '' : '/') + '2、原始图片';
            const created = await window.electronAPI.createFolder(imageFolderPath);
            if (created) {
                this.imagesFolderPath = imageFolderPath;
                console.log('创建了图片文件夹: 2、原始图片');
            }
        } else {
            // 浏览器环境
            for await (const [name, handle] of this.workspaceHandle.entries()) {
                if (handle.kind === 'directory' && possibleNames.includes(name)) {
                    this.imagesFolderHandle = handle;
                    console.log(`找到图片文件夹: ${name}`);
                    return;
                }
            }

            // 如果没找到，尝试创建
            try {
                this.imagesFolderHandle = await this.workspaceHandle.getDirectoryHandle('2、原始图片', { create: true });
                console.log('创建了图片文件夹: 2、原始图片');
            } catch (error) {
                console.warn('无法创建图片文件夹:', error);
            }
        }
    }

    /**
     * 查找JSON文件夹
     */
    async findJsonFolder() {
        const possibleNames = ['1、交付json', '交付json', 'json', 'output', '输出'];

        if (this.isElectron) {
            // Electron环境
            const items = await window.electronAPI.readFolder(this.workspacePath);
            for (const item of items) {
                if (item.isDirectory && possibleNames.includes(item.name)) {
                    this.jsonFolderPath = item.path;
                    console.log(`找到JSON文件夹: ${item.name}`);
                    return;
                }
            }

            // 如果没找到，尝试创建
            const jsonFolderPath = this.workspacePath + (this.workspacePath.endsWith('/') || this.workspacePath.endsWith('\\') ? '' : '/') + '1、交付json';
            const created = await window.electronAPI.createFolder(jsonFolderPath);
            if (created) {
                this.jsonFolderPath = jsonFolderPath;
                console.log('创建了JSON文件夹: 1、交付json');
            }
        } else {
            // 浏览器环境
            for await (const [name, handle] of this.workspaceHandle.entries()) {
                if (handle.kind === 'directory' && possibleNames.includes(name)) {
                    this.jsonFolderHandle = handle;
                    console.log(`找到JSON文件夹: ${name}`);
                    return;
                }
            }

            // 如果没找到，尝试创建
            try {
                this.jsonFolderHandle = await this.workspaceHandle.getDirectoryHandle('1、交付json', { create: true });
                console.log('创建了JSON文件夹: 1、交付json');
            } catch (error) {
                console.warn('无法创建JSON文件夹:', error);
            }
        }
    }

    /**
     * 统计图片文件夹中的图片数量
     */
    async countImagesInFolder() {
        if (this.isElectron) {
            if (!this.imagesFolderPath) return 0;

            try {
                const imageFiles = await window.electronAPI.getImageFiles(this.imagesFolderPath);
                return imageFiles.length;
            } catch (error) {
                console.error('统计图片数量失败:', error);
                return 0;
            }
        } else {
            if (!this.imagesFolderHandle) return 0;

            let count = 0;
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

            try {
                for await (const [name, handle] of this.imagesFolderHandle.entries()) {
                    if (handle.kind === 'file') {
                        const extension = name.toLowerCase().substring(name.lastIndexOf('.'));
                        if (imageExtensions.includes(extension)) {
                            count++;
                        }
                    }
                }
            } catch (error) {
                console.error('统计图片数量失败:', error);
            }

            return count;
        }
    }

    /**
     * 从图片文件夹加载图片
     */
    async loadImagesFromWorkspace() {
        const images = [];

        if (this.isElectron) {
            if (!this.imagesFolderPath) {
                Utils.showNotification('未找到图片文件夹', 'warning');
                return [];
            }

            try {
                const imageFiles = await window.electronAPI.getImageFiles(this.imagesFolderPath);

                for (const imageFile of imageFiles) {
                    images.push({
                        id: Utils.generateId(),
                        name: imageFile.name,
                        path: imageFile.path,
                        url: 'file://' + imageFile.path,
                        loaded: false
                    });
                }

                // 按文件名排序（使用自然排序）
                images.sort((a, b) => Utils.naturalCompare(a.name, b.name));
                console.log(`从工作区加载了 ${images.length} 张图片`);

            } catch (error) {
                console.error('从工作区加载图片失败:', error);
                Utils.showNotification('从工作区加载图片失败', 'error');
            }
        } else {
            if (!this.imagesFolderHandle) {
                Utils.showNotification('未找到图片文件夹', 'warning');
                return [];
            }

            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];

            try {
                for await (const [name, handle] of this.imagesFolderHandle.entries()) {
                    if (handle.kind === 'file') {
                        const extension = name.toLowerCase().substring(name.lastIndexOf('.'));
                        if (imageExtensions.includes(extension)) {
                            const file = await handle.getFile();
                            const url = URL.createObjectURL(file);

                            images.push({
                                id: Utils.generateId(),
                                name: name,
                                file: file,
                                url: url,
                                size: file.size,
                                loaded: false,
                                handle: handle
                            });
                        }
                    }
                }

                // 按文件名排序（使用自然排序）
                images.sort((a, b) => Utils.naturalCompare(a.name, b.name));
                console.log(`从工作区加载了 ${images.length} 张图片`);

            } catch (error) {
                console.error('从工作区加载图片失败:', error);
                Utils.showNotification('从工作区加载图片失败', 'error');
            }
        }

        return images;
    }

    /**
     * 加载现有的JSON文件
     */
    async loadExistingJsonFiles() {
        if (this.isElectron) {
            if (!this.jsonFolderPath) {
                console.log('JSON文件夹未找到，跳过JSON文件加载');
                return;
            }

            try {
                const jsonFiles = await window.electronAPI.getJsonFiles(this.jsonFolderPath);

                for (const jsonFile of jsonFiles) {
                    try {
                        const jsonData = await window.electronAPI.readJsonFile(jsonFile.path);
                        if (jsonData) {
                            this.jsonFiles.set(jsonFile.name, {
                                path: jsonFile.path,
                                data: jsonData,
                                lastModified: Date.now()
                            });
                        }
                    } catch (parseError) {
                        console.warn(`JSON文件解析失败: ${jsonFile.name}`, parseError);
                    }
                }

                console.log(`从JSON文件夹加载了 ${this.jsonFiles.size} 个JSON文件`);

            } catch (error) {
                console.error('加载JSON文件失败:', error);
            }
        } else {
            if (!this.jsonFolderHandle) {
                console.log('JSON文件夹未找到，跳过JSON文件加载');
                return;
            }

            try {
                for await (const [name, handle] of this.jsonFolderHandle.entries()) {
                    if (handle.kind === 'file' && name.endsWith('.json')) {
                        const file = await handle.getFile();
                        const content = await file.text();

                        try {
                            const jsonData = JSON.parse(content);
                            this.jsonFiles.set(name, {
                                handle: handle,
                                data: jsonData,
                                lastModified: file.lastModified
                            });
                        } catch (parseError) {
                            console.warn(`JSON文件解析失败: ${name}`, parseError);
                        }
                    }
                }

                console.log(`从JSON文件夹加载了 ${this.jsonFiles.size} 个JSON文件`);

            } catch (error) {
                console.error('加载JSON文件失败:', error);
            }
        }
    }

    /**
     * 获取指定图片的所有JSON文件
     * @param {string} imageName 图片名称
     * @returns {Array} JSON文件列表
     */
    async getImageJsonFiles(imageName) {
        const imageBaseName = imageName.substring(0, imageName.lastIndexOf('.')) || imageName;
        const pattern = `${imageBaseName}大题`;
        const files = [];

        if (this.isElectron) {
            if (!this.jsonFolderPath) return files;

            try {
                const allFiles = await window.electronAPI.readDirectory(this.jsonFolderPath);
                for (const file of allFiles) {
                    if (file.startsWith(pattern) && file.endsWith('.json')) {
                        // 提取大题编号
                        const match = file.match(new RegExp(`${pattern}(\\d+)\\.json$`));
                        if (match) {
                            files.push({
                                filename: file,
                                questionNumber: parseInt(match[1])
                            });
                        }
                    }
                }
            } catch (error) {
                console.error('读取JSON文件夹失败:', error);
            }
        } else {
            if (!this.jsonFolderHandle) return files;

            try {
                for await (const [name, handle] of this.jsonFolderHandle.entries()) {
                    if (handle.kind === 'file' && name.startsWith(pattern) && name.endsWith('.json')) {
                        // 提取大题编号
                        const match = name.match(new RegExp(`${pattern}(\\d+)\\.json$`));
                        if (match) {
                            files.push({
                                filename: name,
                                questionNumber: parseInt(match[1])
                            });
                        }
                    }
                }
            } catch (error) {
                console.error('读取JSON文件夹失败:', error);
            }
        }

        return files;
    }

    /**
     * 删除指定的JSON文件
     * @param {string} imageName 图片名称
     * @param {number} questionNumber 大题编号
     * @returns {boolean} 是否删除成功
     */
    async deleteJsonFromWorkspace(imageName, questionNumber) {
        const imageBaseName = imageName.substring(0, imageName.lastIndexOf('.')) || imageName;
        const filename = `${imageBaseName}大题${questionNumber}.json`;

        if (this.isElectron) {
            if (!this.jsonFolderPath) return false;

            try {
                const filePath = this.jsonFolderPath + (this.jsonFolderPath.endsWith('/') || this.jsonFolderPath.endsWith('\\') ? '' : '/') + filename;
                const success = await window.electronAPI.deleteFile(filePath);

                if (success) {
                    // 从内存中移除记录
                    this.jsonFiles.delete(filename);
                    console.log(`已删除JSON文件: ${filename}`);
                    return true;
                } else {
                    console.error('删除JSON文件失败:', filename);
                    return false;
                }
            } catch (error) {
                console.error('删除JSON文件失败:', error);
                return false;
            }
        } else {
            if (!this.jsonFolderHandle) return false;

            try {
                await this.jsonFolderHandle.removeEntry(filename);
                // 从内存中移除记录
                this.jsonFiles.delete(filename);
                console.log(`已删除JSON文件: ${filename}`);
                return true;
            } catch (error) {
                if (error.name !== 'NotFoundError') {
                    console.error('删除JSON文件失败:', error);
                }
                return false;
            }
        }
    }

    /**
     * 保存JSON文件到工作区
     * @param {string} imageName 图片名称
     * @param {number} questionNumber 大题编号
     * @param {Object} jsonData JSON数据
     */
    async saveJsonToWorkspace(imageName, questionNumber, jsonData) {
        // 生成文件名：{图片名}{大题名}.json
        const imageBaseName = imageName.substring(0, imageName.lastIndexOf('.')) || imageName;
        const filename = `${imageBaseName}大题${questionNumber}.json`;

        if (this.isElectron) {
            if (!this.jsonFolderPath) {
                Utils.showNotification('JSON文件夹未找到', 'error');
                return false;
            }

            try {
                const filePath = this.jsonFolderPath + (this.jsonFolderPath.endsWith('/') || this.jsonFolderPath.endsWith('\\') ? '' : '/') + filename;
                const success = await window.electronAPI.writeJsonFile(filePath, jsonData);

                if (success) {
                    // 更新内存中的记录
                    this.jsonFiles.set(filename, {
                        path: filePath,
                        data: jsonData,
                        lastModified: Date.now()
                    });

                    this.trigger('onJsonSave', { filename, data: jsonData });
                    // Utils.showNotification(`已保存到JSON文件夹: ${filename}`, 'success');
                    return true;
                } else {
                    Utils.showNotification('保存JSON文件失败', 'error');
                    return false;
                }

            } catch (error) {
                console.error('保存JSON文件失败:', error);
                Utils.showNotification('保存JSON文件失败: ' + error.message, 'error');
                return false;
            }
        } else {
            if (!this.jsonFolderHandle) {
                Utils.showNotification('JSON文件夹未找到', 'error');
                return false;
            }

            try {
                // 在JSON文件夹中获取或创建文件句柄
                const fileHandle = await this.jsonFolderHandle.getFileHandle(filename, { create: true });

                // 写入文件
                const writable = await fileHandle.createWritable();
                const jsonString = JSON.stringify(jsonData, null, 2);
                await writable.write(jsonString);
                await writable.close();

                // 更新内存中的记录
                this.jsonFiles.set(filename, {
                    handle: fileHandle,
                    data: jsonData,
                    lastModified: Date.now()
                });

                this.trigger('onJsonSave', { filename, data: jsonData });
                // Utils.showNotification(`已保存到JSON文件夹: ${filename}`, 'success');

                return true;

            } catch (error) {
                console.error('保存JSON文件失败:', error);
                Utils.showNotification('保存JSON文件失败: ' + error.message, 'error');
                return false;
            }
        }
    }

    /**
     * 从工作区加载JSON文件
     * @param {string} imageName 图片名称
     * @param {number} questionNumber 大题编号
     * @returns {Object|null} JSON数据
     */
    async loadJsonFromWorkspace(imageName, questionNumber) {
        if (!this.isWorkspaceLoaded) return null;

        try {
            const imageBaseName = imageName.substring(0, imageName.lastIndexOf('.')) || imageName;
            const filename = `${imageBaseName}大题${questionNumber}.json`;

            const jsonInfo = this.jsonFiles.get(filename);
            if (jsonInfo) {
                this.trigger('onJsonLoad', { filename, data: jsonInfo.data });
                return jsonInfo.data;
            }

        } catch (error) {
            console.error('加载JSON文件失败:', error);
        }

        return null;
    }

    /**
     * 获取图片对应的所有JSON文件
     * @param {string} imageName 图片名称
     * @returns {Array} JSON文件列表
     */
    getJsonFilesForImage(imageName) {
        const imageBaseName = imageName.substring(0, imageName.lastIndexOf('.')) || imageName;
        const files = [];
        
        for (const [filename, jsonInfo] of this.jsonFiles.entries()) {
            if (filename.startsWith(imageBaseName + '大题')) {
                const questionMatch = filename.match(/大题(\d+)\.json$/);
                if (questionMatch) {
                    files.push({
                        filename: filename,
                        questionNumber: parseInt(questionMatch[1]),
                        data: jsonInfo.data,
                        lastModified: jsonInfo.lastModified
                    });
                }
            }
        }
        
        // 按题号数字大小排序，而不是字符串排序
        return files.sort((a, b) => a.questionNumber - b.questionNumber);
    }

    /**
     * 获取工作区状态
     */
    getWorkspaceStatus() {
        const workspaceName = this.isElectron ?
            (this.workspacePath ? this.workspacePath.split(/[/\\]/).pop() : null) :
            (this.workspaceHandle?.name || null);

        return {
            isLoaded: this.isWorkspaceLoaded,
            workspaceName: workspaceName,
            hasImageFolder: this.isElectron ? !!this.imagesFolderPath : !!this.imagesFolderHandle,
            hasJsonFolder: this.isElectron ? !!this.jsonFolderPath : !!this.jsonFolderHandle,
            jsonFileCount: this.jsonFiles.size,
            isSupported: this.isFileSystemAPISupported()
        };
    }

    /**
     * 关闭工作区
     */
    closeWorkspace() {
        this.workspacePath = null;
        this.workspaceHandle = null;
        this.imagesFolderPath = null;
        this.imagesFolderHandle = null;
        this.jsonFolderPath = null;
        this.jsonFolderHandle = null;
        this.jsonFiles.clear();
        this.isWorkspaceLoaded = false;

        Utils.showNotification('工作区已关闭', 'info');
    }
}

// 导出工作区管理器类
window.WorkspaceManager = WorkspaceManager;
